import {
  createRouter,
  createWebHashHistory,
  type RouteRecordRaw,
} from "vue-router";
import Home from "../views/Home.vue";
import RSSArticle from "../views/RSSArticle.vue";
import MessageStyleTest from "../views/MessageStyleTest.vue";

// 定义路由规则
const routes: RouteRecordRaw[] = [
  {
    path: "/",
    name: "Home",
    component: Home,
    meta: {
      title: "首页",
      utoolsCode: "home", // 对应 utools 插件的 code
    },
  },
  {
    path: "/rss/article",
    name: "RSSArticle",
    component: RSSArticle,
    meta: {
      title: "RSS 文章详情",
      utoolsCode: "rss-article",
    },
  },
  {
    path: "/message-style-test",
    name: "MessageStyleTest",
    component: MessageStyleTest,
    meta: {
      title: "Message 样式测试",
      utoolsCode: "message-style-test",
    },
  },
];

// 创建路由实例
const router = createRouter({
  // 使用 hash 模式，适合 utools 插件环境
  history: createWebHashHistory(),
  routes,
});

// 路由守卫
router.beforeEach((to, from, next) => {
  // 可以在这里添加路由守卫逻辑
  console.log("路由跳转:", from.path, "->", to.path);
  next();
});

export default router;

// 导出路由相关的工具函数
export const getRouteByUtoolsCode = (code: string) => {
  return routes.find((route) => route.meta?.utoolsCode === code);
};

export const navigateToUtoolsCode = (code: string) => {
  const route = getRouteByUtoolsCode(code);
  if (route) {
    router.push(route.path);
  } else {
    // 默认跳转到首页
    router.push("/");
  }
};

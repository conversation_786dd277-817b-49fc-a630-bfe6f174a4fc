<template>
  <div class="tooltip-bug-test">
    <div class="test-header">
      <h1 class="test-title">Tooltip Bug 修复测试</h1>
      <p class="test-description">
        测试在并排按钮间快速移动鼠标时，Tooltip 是否会卡住不消失的问题。
      </p>
    </div>

    <!-- 测试场景 1: 并排按钮 -->
    <section class="test-section">
      <h3 class="section-title">测试场景 1: 并排按钮快速切换</h3>
      <p class="section-desc">在这些按钮之间快速移动鼠标，观察 Tooltip 是否会卡住：</p>
      
      <div class="button-row">
        <Tooltip content="刷新数据" placement="top">
          <button class="test-btn">
            <div class="i-material-symbols-refresh text-lg" />
            刷新
          </button>
        </Tooltip>

        <Tooltip content="添加新项" placement="top">
          <button class="test-btn">
            <div class="i-material-symbols-add text-lg" />
            添加
          </button>
        </Tooltip>

        <Tooltip content="编辑内容" placement="top">
          <button class="test-btn">
            <div class="i-material-symbols-edit text-lg" />
            编辑
          </button>
        </Tooltip>

        <Tooltip content="删除项目" placement="top">
          <button class="test-btn danger">
            <div class="i-material-symbols-delete text-lg" />
            删除
          </button>
        </Tooltip>

        <Tooltip content="更多选项" placement="top">
          <button class="test-btn">
            <div class="i-material-symbols-more-horiz text-lg" />
            更多
          </button>
        </Tooltip>
      </div>
    </section>

    <!-- 测试场景 2: 图标按钮 -->
    <section class="test-section">
      <h3 class="section-title">测试场景 2: 紧密排列的图标按钮</h3>
      <p class="section-desc">这些图标按钮间距更小，更容易触发 bug：</p>
      
      <div class="icon-row">
        <Tooltip content="首页" placement="bottom">
          <button class="icon-btn">
            <div class="i-material-symbols-home text-xl" />
          </button>
        </Tooltip>

        <Tooltip content="搜索" placement="bottom">
          <button class="icon-btn">
            <div class="i-material-symbols-search text-xl" />
          </button>
        </Tooltip>

        <Tooltip content="收藏" placement="bottom">
          <button class="icon-btn">
            <div class="i-material-symbols-bookmark text-xl" />
          </button>
        </Tooltip>

        <Tooltip content="分享" placement="bottom">
          <button class="icon-btn">
            <div class="i-material-symbols-share text-xl" />
          </button>
        </Tooltip>

        <Tooltip content="下载" placement="bottom">
          <button class="icon-btn">
            <div class="i-material-symbols-download text-xl" />
          </button>
        </Tooltip>

        <Tooltip content="设置" placement="bottom">
          <button class="icon-btn">
            <div class="i-material-symbols-settings text-xl" />
          </button>
        </Tooltip>
      </div>
    </section>

    <!-- 测试场景 3: 垂直排列 -->
    <section class="test-section">
      <h3 class="section-title">测试场景 3: 垂直排列的按钮</h3>
      <p class="section-desc">垂直方向的快速移动测试：</p>
      
      <div class="vertical-buttons">
        <Tooltip content="向上移动" placement="right">
          <button class="vertical-btn">
            <div class="i-material-symbols-keyboard-arrow-up text-lg" />
            向上
          </button>
        </Tooltip>

        <Tooltip content="向下移动" placement="right">
          <button class="vertical-btn">
            <div class="i-material-symbols-keyboard-arrow-down text-lg" />
            向下
          </button>
        </Tooltip>

        <Tooltip content="向左移动" placement="right">
          <button class="vertical-btn">
            <div class="i-material-symbols-keyboard-arrow-left text-lg" />
            向左
          </button>
        </Tooltip>

        <Tooltip content="向右移动" placement="right">
          <button class="vertical-btn">
            <div class="i-material-symbols-keyboard-arrow-right text-lg" />
            向右
          </button>
        </Tooltip>
      </div>
    </section>

    <!-- 测试说明 -->
    <section class="test-section">
      <h3 class="section-title">测试说明</h3>
      <div class="test-instructions">
        <h4>如何测试：</h4>
        <ol>
          <li>在按钮之间快速移动鼠标</li>
          <li>观察 Tooltip 是否能正确显示和隐藏</li>
          <li>特别注意是否有 Tooltip 卡住不消失的情况</li>
          <li>尝试从不同角度和速度移动鼠标</li>
        </ol>

        <h4>修复的问题：</h4>
        <ul>
          <li>✅ 定时器冲突导致的显示/隐藏异常</li>
          <li>✅ 快速移动时状态管理混乱</li>
          <li>✅ 鼠标悬停在 Tooltip 上时的处理</li>
          <li>✅ 全局鼠标移动检测作为兜底方案</li>
        </ul>

        <h4>技术改进：</h4>
        <ul>
          <li>改进了定时器管理逻辑</li>
          <li>添加了鼠标状态跟踪</li>
          <li>支持鼠标悬停在 Tooltip 上</li>
          <li>添加了全局鼠标移动检测</li>
        </ul>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import { Tooltip } from '@/components/Tooltip'
</script>

<style scoped>
.tooltip-bug-test {
  padding: 24px;
  max-width: 1000px;
  margin: 0 auto;
  color: #e4e4e7;
  background: #313239;
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 48px;
  padding: 32px;
  background: rgba(55, 65, 81, 0.3);
  border-radius: 16px;
  border: 1px solid #4b5563;
}

.test-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #f9fafb;
  background: linear-gradient(135deg, #ef4444 0%, #f97316 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.test-description {
  font-size: 16px;
  color: #d1d5db;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 32px;
  padding: 24px;
  background: rgba(55, 65, 81, 0.3);
  border-radius: 12px;
  border: 1px solid #4b5563;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #f3f4f6;
}

.section-desc {
  font-size: 14px;
  color: #9ca3af;
  margin-bottom: 20px;
  line-height: 1.5;
}

/* 按钮行样式 */
.button-row {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.test-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border: 1px solid #6b7280;
  border-radius: 8px;
  color: #d1d5db;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.test-btn:hover {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
  border-color: #9ca3af;
  color: #f9fafb;
  transform: translateY(-1px);
}

.test-btn.danger {
  background: linear-gradient(135deg, #7f1d1d 0%, #991b1b 100%);
  border-color: #dc2626;
}

.test-btn.danger:hover {
  background: linear-gradient(135deg, #991b1b 0%, #b91c1c 100%);
  border-color: #ef4444;
}

/* 图标按钮样式 */
.icon-row {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.icon-btn {
  width: 48px;
  height: 48px;
  padding: 12px;
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border: 1px solid #6b7280;
  border-radius: 8px;
  color: #d1d5db;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-btn:hover {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
  border-color: #9ca3af;
  color: #f9fafb;
  transform: translateY(-1px);
}

/* 垂直按钮样式 */
.vertical-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  max-width: 200px;
  margin: 0 auto;
}

.vertical-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border: 1px solid #6b7280;
  border-radius: 8px;
  color: #d1d5db;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  width: 100%;
  justify-content: flex-start;
}

.vertical-btn:hover {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
  border-color: #9ca3af;
  color: #f9fafb;
  transform: translateX(4px);
}

/* 测试说明样式 */
.test-instructions {
  font-size: 14px;
  line-height: 1.6;
  color: #d1d5db;
}

.test-instructions h4 {
  color: #f3f4f6;
  margin: 16px 0 8px 0;
  font-size: 15px;
  font-weight: 600;
}

.test-instructions ol,
.test-instructions ul {
  margin: 8px 0 16px 20px;
}

.test-instructions li {
  margin: 4px 0;
}

.test-instructions ul li {
  list-style-type: none;
  position: relative;
}

.test-instructions ul li::before {
  content: "•";
  color: #60a5fa;
  font-weight: bold;
  position: absolute;
  left: -16px;
}
</style>

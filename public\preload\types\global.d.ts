/**
 * uTools 预加载脚本全局类型定义
 */

// uTools API 基础类型定义
interface UToolsApi {
  // 插件生命周期
  onPluginEnter(
    callback: (data: { code: string; type: string; payload: any }) => void
  ): void;
  onPluginOut(callback: () => void): void;

  // 窗口操作
  setExpendHeight(height: number): void;
  hideMainWindow(): void;
  showMainWindow(): void;

  // 系统操作
  shellOpenExternal(url: string): void;
  shellOpenPath(path: string): void;
  shellShowItemInFolder(path: string): void;

  // 通知
  showNotification(message: string): void;

  // 剪贴板
  copyText(text: string): void;
  copyImage(image: any): void;

  // 对话框
  showSaveDialog(options: {
    title?: string;
    defaultPath?: string;
    filters?: Array<{ name: string; extensions: string[] }>;
  }): string | undefined;

  // 路由跳转
  redirect(code: string, data?: any): void;

  // ubrowser相关方法
  ubrowser: {
    goto(url: string, headers?: Record<string, string>, timeout?: number): any;
    run(options?: any): Promise<any>;
    // 可以根据需要添加更多ubrowser方法
  };

  // Electron 相关 API（如果可用）
  electron?: {
    net?: {
      request(options: any): any;
    };
    session?: {
      defaultSession: any;
    };
  };
}

// 全局声明
declare global {
  const utools: UToolsApi;

  interface Window {
    utoolsPayload?: any;
    utoolsAPI?: any;
  }
}

// 插件进入数据类型
export interface PluginEnterData {
  code: string;
  type: string;
  payload: any;
}

// 文件信息类型
export interface FileInfo {
  name: string;
  path: string;
  size: number;
  modified: Date;
  extension: string;
}

// 导出空对象以使此文件成为模块
export {};

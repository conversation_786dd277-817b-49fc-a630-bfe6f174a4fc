/**
 * 图片代理服务
 * 基于 Fluent Reader 的经验，使用 uTools 的 Electron 环境处理图片防盗链问题
 */

export interface ImageProxyOptions {
  url: string;
  referrer?: string;
  userAgent?: string;
  timeout?: number;
}

export interface ImageProxyResult {
  success: boolean;
  dataUrl?: string;
  error?: string;
  originalUrl: string;
}

export class ImageProxy {
  private static readonly DEFAULT_USER_AGENT =
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";

  private static readonly DEFAULT_TIMEOUT = 10000; // 10秒超时

  /**
   * 检测图片是否需要代理处理
   */
  static needsProxy(url: string): boolean {
    if (!url) return false;

    // 已知需要特殊处理的域名
    const proxyDomains = [
      "image.gcores.com",
      "img.gcores.com",
      "static.gcores.com",
      "cdn.sspai.com",
      "img.sspai.com",
      "pic.36krcnd.com",
      "img.36krcdn.com",
      "inews.gtimg.com",
      "mat1.gtimg.com",
      "p.qpic.cn",
      "mmbiz.qpic.cn",
      "wx1.sinaimg.cn",
      "wx2.sinaimg.cn",
      "wx3.sinaimg.cn",
      "wx4.sinaimg.cn",
      "tva1.sinaimg.cn",
      "tva2.sinaimg.cn",
      "tva3.sinaimg.cn",
      "tva4.sinaimg.cn",
      "img.alicdn.com",
      "gw.alicdn.com",
      "img.t.sinajs.cn",
      "n.sinaimg.cn",
      "k.sinaimg.cn",
      "upload-images.jianshu.io",
      "cdn2.jianshu.io",
      "img.shields.io",
      "files.toodaylab.com", // 理想生活实验室
    ];

    // 检查是否包含已知的防盗链域名
    return proxyDomains.some((domain) => url.includes(domain));
  }

  /**
   * 检测是否为阿里云 OSS 或其他云存储
   */
  static isCloudStorage(url: string): boolean {
    if (!url) return false;

    const cloudPatterns = [
      "aliyuncs.com",
      "oss-cn-",
      "x-oss-process",
      "amazonaws.com",
      "s3.",
      "blob.core.windows.net",
      "cos.ap-",
      "myqcloud.com",
    ];

    return cloudPatterns.some((pattern) => url.includes(pattern));
  }

  /**
   * 使用 uTools 的 ubrowser 获取图片
   */
  static async fetchImageWithUBrowser(
    options: ImageProxyOptions
  ): Promise<ImageProxyResult> {
    const {
      url,
      referrer = "",
      userAgent = this.DEFAULT_USER_AGENT,
      timeout = this.DEFAULT_TIMEOUT,
    } = options;

    try {
      console.log(`[ImageProxy] 使用 ubrowser 获取图片: ${url}`);

      // 检查 ubrowser 是否可用
      if (!utools.ubrowser) {
        throw new Error("ubrowser 不可用");
      }

      // 创建一个临时的 HTML 页面来获取图片
      const proxyHtml = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="UTF-8">
          <meta name="referrer" content="no-referrer">
        </head>
        <body>
          <script>
            (async function() {
              try {
                const response = await fetch('${url}', {
                  method: 'GET',
                  headers: {
                    'User-Agent': '${userAgent}',
                    'Referer': '${referrer}',
                    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                  },
                  mode: 'cors',
                  credentials: 'omit'
                });

                if (!response.ok) {
                  throw new Error(\`HTTP \${response.status}: \${response.statusText}\`);
                }

                const blob = await response.blob();
                const reader = new FileReader();
                reader.onload = function() {
                  window.parent.postMessage({
                    type: 'image-proxy-result',
                    success: true,
                    dataUrl: reader.result,
                    originalUrl: '${url}'
                  }, '*');
                };
                reader.onerror = function() {
                  window.parent.postMessage({
                    type: 'image-proxy-result',
                    success: false,
                    error: 'Failed to convert blob to data URL',
                    originalUrl: '${url}'
                  }, '*');
                };
                reader.readAsDataURL(blob);
              } catch (error) {
                window.parent.postMessage({
                  type: 'image-proxy-result',
                  success: false,
                  error: error.message,
                  originalUrl: '${url}'
                }, '*');
              }
            })();
          </script>
        </body>
        </html>
      `;

      // 使用 ubrowser 执行代理请求
      const browser = utools.ubrowser.goto(
        `data:text/html;charset=utf-8,${encodeURIComponent(proxyHtml)}`
      );

      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error("请求超时"));
        }, timeout);

        // 监听消息
        const messageHandler = (event: MessageEvent) => {
          if (event.data && event.data.type === "image-proxy-result") {
            clearTimeout(timeoutId);
            window.removeEventListener("message", messageHandler);
            resolve(event.data as ImageProxyResult);
          }
        };

        window.addEventListener("message", messageHandler);

        // 运行 ubrowser
        browser
          .run({
            width: 1,
            height: 1,
            show: false,
          })
          .catch(reject);
      });
    } catch (error) {
      console.error(`[ImageProxy] ubrowser 获取图片失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        originalUrl: url,
      };
    }
  }

  /**
   * 使用传统方法获取图片（降级方案）
   */
  static async fetchImageFallback(
    options: ImageProxyOptions
  ): Promise<ImageProxyResult> {
    const { url, timeout = this.DEFAULT_TIMEOUT } = options;

    try {
      console.log(`[ImageProxy] 使用传统方法获取图片: ${url}`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: "GET",
        headers: {
          "User-Agent": this.DEFAULT_USER_AGENT,
          Accept: "image/webp,image/apng,image/*,*/*;q=0.8",
          "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
          "Cache-Control": "no-cache",
          Pragma: "no-cache",
        },
        mode: "cors",
        credentials: "omit",
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const blob = await response.blob();

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => {
          resolve({
            success: true,
            dataUrl: reader.result as string,
            originalUrl: url,
          });
        };
        reader.onerror = () => {
          reject(new Error("Failed to convert blob to data URL"));
        };
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      console.error(`[ImageProxy] 传统方法获取图片失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        originalUrl: url,
      };
    }
  }

  /**
   * 主要的图片代理方法
   */
  static async proxyImage(
    options: ImageProxyOptions
  ): Promise<ImageProxyResult> {
    const { url } = options;

    if (!url) {
      return {
        success: false,
        error: "图片 URL 为空",
        originalUrl: url,
      };
    }

    // 如果是 data URL 或 blob URL，直接返回
    if (url.startsWith("data:") || url.startsWith("blob:")) {
      return {
        success: true,
        dataUrl: url,
        originalUrl: url,
      };
    }

    // 优先使用 ubrowser 方法
    if (utools.ubrowser) {
      const result = await this.fetchImageWithUBrowser(options);
      if (result.success) {
        return result;
      }
      console.warn(
        `[ImageProxy] ubrowser 方法失败，尝试降级方案: ${result.error}`
      );
    }

    // 降级到传统方法
    return this.fetchImageFallback(options);
  }

  /**
   * 批量代理图片
   */
  static async proxyImages(
    urls: string[],
    options: Partial<ImageProxyOptions> = {}
  ): Promise<ImageProxyResult[]> {
    const promises = urls.map((url) => this.proxyImage({ ...options, url }));

    return Promise.all(promises);
  }
}

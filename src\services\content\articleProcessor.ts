/**
 * 文章内容处理器
 * 基于 Fluent Reader 的经验，提供智能的内容处理和图片优化
 */

import { proxyImage, needsImageProxy } from "../utils/imageUtils";

export interface ArticleProcessorOptions {
  baseUrl?: string;
  enableImageProxy?: boolean;
  enableImageOptimization?: boolean;
  timeout?: number;
}

export class ArticleProcessor {
  private static readonly DEFAULT_OPTIONS: ArticleProcessorOptions = {
    enableImageProxy: true,
    enableImageOptimization: true,
    timeout: 10000,
  };

  /**
   * 处理文章内容
   */
  static async processContent(
    content: string,
    articleUrl: string,
    options: ArticleProcessorOptions = {}
  ): Promise<string> {
    if (!content) return content;

    const opts = { ...this.DEFAULT_OPTIONS, ...options };

    try {
      // 创建 DOM 解析器
      const parser = new DOMParser();
      const doc = parser.parseFromString(content, "text/html");

      // 设置 base URL 用于相对路径解析
      if (articleUrl) {
        const baseUrl = opts.baseUrl || this.extractBaseUrl(articleUrl);
        let baseEl = doc.querySelector("base");
        if (!baseEl) {
          baseEl = doc.createElement("base");
          doc.head.appendChild(baseEl);
        }
        baseEl.setAttribute("href", baseUrl);
      }

      // 处理图片
      if (opts.enableImageProxy || opts.enableImageOptimization) {
        await this.processImages(doc, opts);
      }

      // 处理链接
      this.processLinks(doc, articleUrl);

      // 清理脚本标签（安全考虑）
      this.removeScripts(doc);

      // 优化媒体内容
      this.optimizeMedia(doc);

      return doc.body.innerHTML;
    } catch (error) {
      console.error("[ArticleProcessor] 处理文章内容失败:", error);
      return content; // 返回原始内容
    }
  }

  /**
   * 处理图片
   */
  private static async processImages(
    doc: Document,
    options: ArticleProcessorOptions
  ): Promise<void> {
    const images = doc.querySelectorAll("img");
    const imagePromises: Promise<void>[] = [];

    images.forEach((img) => {
      const promise = this.processImage(img, options);
      imagePromises.push(promise);
    });

    // 并行处理所有图片
    await Promise.allSettled(imagePromises);
  }

  /**
   * 处理单个图片
   */
  private static async processImage(
    img: HTMLImageElement,
    options: ArticleProcessorOptions
  ): Promise<void> {
    try {
      let originalSrc = img.getAttribute("src");
      if (!originalSrc) return;

      // 处理协议相对 URL (以 // 开头)
      if (originalSrc.startsWith("//")) {
        originalSrc = "https:" + originalSrc;
        img.setAttribute("src", originalSrc);
      }

      // 保存原始 src
      img.setAttribute("data-original-src", originalSrc);

      // 转换相对路径为绝对路径
      let src = img.src; // 浏览器会自动转换为绝对路径

      // 使用图片代理
      if (options.enableImageProxy && needsImageProxy(src)) {
        try {
          const proxiedSrc = await proxyImage(src);
          if (proxiedSrc !== src) {
            img.setAttribute("src", proxiedSrc);
            img.setAttribute("data-proxied", "true");
            console.log(
              `[ArticleProcessor] 图片代理成功: ${src} -> ${proxiedSrc.substring(
                0,
                50
              )}...`
            );
            return;
          }
        } catch (error) {
          console.warn(`[ArticleProcessor] 图片代理失败: ${src}`, error);
        }
      }

      // 图片优化
      if (options.enableImageOptimization) {
        this.optimizeImage(img, src);
      }
    } catch (error) {
      console.error("[ArticleProcessor] 处理图片失败:", error);
    }
  }

  /**
   * 优化图片
   */
  private static optimizeImage(img: HTMLImageElement, src: string): void {
    // 设置 referrer policy
    img.setAttribute("referrerpolicy", "no-referrer");

    // 为云存储图片添加时间戳
    if (this.isCloudStorageImage(src)) {
      const timestamp = Date.now();
      const optimizedSrc = `${src}${
        src.includes("?") ? "&" : "?"
      }_t=${timestamp}`;
      img.setAttribute("src", optimizedSrc);
    }

    // 添加懒加载
    if (!img.hasAttribute("loading")) {
      img.setAttribute("loading", "lazy");
    }

    // 设置图片样式
    img.style.maxWidth = "100%";
    img.style.height = "auto";

    // 添加错误处理
    img.addEventListener("error", this.handleImageError.bind(null, img));
  }

  /**
   * 检查是否为云存储图片
   */
  private static isCloudStorageImage(url: string): boolean {
    const cloudPatterns = [
      "aliyuncs.com",
      "oss-cn-",
      "x-oss-process",
      "amazonaws.com",
      "s3.",
      "blob.core.windows.net",
      "cos.ap-",
      "myqcloud.com",
      "qcloud.com",
    ];

    return cloudPatterns.some((pattern) => url.includes(pattern));
  }

  /**
   * 处理图片加载错误
   */
  private static handleImageError(img: HTMLImageElement, _event: Event): void {
    console.warn("[ArticleProcessor] 图片加载失败:", img.src);

    const originalSrc = img.getAttribute("data-original-src");
    if (originalSrc && !img.hasAttribute("data-retry")) {
      // 标记为重试状态
      img.setAttribute("data-retry", "true");

      // 尝试使用原始 URL
      img.src = originalSrc;
    } else {
      // 隐藏失败的图片
      img.style.display = "none";
    }
  }

  /**
   * 处理链接
   */
  private static processLinks(doc: Document, _articleUrl: string): void {
    const links = doc.querySelectorAll("a");

    links.forEach((link) => {
      // 确保外部链接在新窗口打开
      const href = link.getAttribute("href");
      if (href && (href.startsWith("http://") || href.startsWith("https://"))) {
        link.setAttribute("target", "_blank");
        link.setAttribute("rel", "noopener noreferrer");
      }
    });
  }

  /**
   * 移除脚本标签
   */
  private static removeScripts(doc: Document): void {
    const scripts = doc.querySelectorAll("script");
    scripts.forEach((script) => script.remove());
  }

  /**
   * 优化媒体内容
   */
  private static optimizeMedia(doc: Document): void {
    // 处理 iframe
    const iframes = doc.querySelectorAll("iframe");
    iframes.forEach((iframe) => {
      iframe.style.maxWidth = "100%";
      iframe.setAttribute("loading", "lazy");
    });

    // 处理视频
    const videos = doc.querySelectorAll("video");
    videos.forEach((video) => {
      video.style.maxWidth = "100%";
      video.setAttribute("controls", "true");
    });
  }

  /**
   * 提取基础 URL
   */
  private static extractBaseUrl(url: string): string {
    try {
      const urlObj = new URL(url);
      return `${urlObj.protocol}//${urlObj.host}`;
    } catch {
      return "";
    }
  }

  /**
   * 从内容中提取第一张图片（用于缩略图）
   */
  static async extractThumbnail(
    content: string,
    articleUrl?: string
  ): Promise<string | null> {
    if (!content) return null;

    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(content, "text/html");

      // 设置 base URL
      if (articleUrl) {
        const baseUrl = this.extractBaseUrl(articleUrl);
        const baseEl = doc.createElement("base");
        baseEl.setAttribute("href", baseUrl);
        doc.head.appendChild(baseEl);
      }

      const img = doc.querySelector("img");
      if (img && img.src) {
        let src = img.src;

        // 尝试使用图片代理
        if (needsImageProxy(src)) {
          try {
            src = await proxyImage(src);
          } catch (error) {
            console.warn("[ArticleProcessor] 缩略图代理失败:", error);
          }
        }

        // 验证图片 URL
        if (
          src.startsWith("http://") ||
          src.startsWith("https://") ||
          src.startsWith("data:")
        ) {
          return src;
        }
      }

      return null;
    } catch (error) {
      console.error("[ArticleProcessor] 提取缩略图失败:", error);
      return null;
    }
  }

  /**
   * 清理和标准化 HTML 内容
   */
  static sanitizeContent(content: string): string {
    if (!content) return content;

    try {
      const parser = new DOMParser();
      const doc = parser.parseFromString(content, "text/html");

      // 移除危险元素
      const dangerousElements = doc.querySelectorAll(
        "script, object, embed, applet, form"
      );
      dangerousElements.forEach((el) => el.remove());

      // 清理危险属性
      const allElements = doc.querySelectorAll("*");
      allElements.forEach((el) => {
        const dangerousAttrs = [
          "onclick",
          "onload",
          "onerror",
          "onmouseover",
          "onfocus",
        ];
        dangerousAttrs.forEach((attr) => {
          if (el.hasAttribute(attr)) {
            el.removeAttribute(attr);
          }
        });
      });

      return doc.body.innerHTML;
    } catch (error) {
      console.error("[ArticleProcessor] 内容清理失败:", error);
      return content;
    }
  }
}

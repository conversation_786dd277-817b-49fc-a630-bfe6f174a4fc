import Prism from 'prismjs';

// 只导入需要的语言组件（按需加载）
import 'prismjs/components/prism-javascript';
import 'prismjs/components/prism-typescript';
import 'prismjs/components/prism-python';
import 'prismjs/components/prism-java';
import 'prismjs/components/prism-css';
import 'prismjs/components/prism-json';
import 'prismjs/components/prism-yaml';
import 'prismjs/components/prism-bash';
import 'prismjs/components/prism-sql';
import 'prismjs/components/prism-markdown';
import 'prismjs/components/prism-jsx';
import 'prismjs/components/prism-tsx';

/**
 * 轻量级代码高亮服务
 * 基于 Prism.js，体积小，性能好
 */
class HighlightService {
  // 支持的语言映射
  private readonly languageMap: Record<string, string> = {
    'javascript': 'javascript',
    'js': 'javascript',
    'typescript': 'typescript',
    'ts': 'typescript',
    'python': 'python',
    'py': 'python',
    'java': 'java',
    'html': 'markup',
    'xml': 'markup',
    'css': 'css',
    'scss': 'css',
    'sass': 'css',
    'less': 'css',
    'json': 'json',
    'yaml': 'yaml',
    'yml': 'yaml',
    'bash': 'bash',
    'shell': 'bash',
    'sh': 'bash',
    'sql': 'sql',
    'markdown': 'markdown',
    'md': 'markdown',
    'jsx': 'jsx',
    'tsx': 'tsx',
    'vue': 'markup', // Vue 模板使用 markup 高亮
  };

  /**
   * 检测代码语言
   */
  private detectLanguage(code: string, hint?: string): string {
    // 如果有明确的语言提示，优先使用
    if (hint && this.languageMap[hint.toLowerCase()]) {
      return this.languageMap[hint.toLowerCase()];
    }

    // 基于代码内容进行简单的语言检测
    const trimmedCode = code.trim();

    // JavaScript/TypeScript 检测
    if (
      /^(import|export|const|let|var|function|class|interface|type)\s/.test(trimmedCode) ||
      /console\.(log|error|warn)/.test(code) ||
      /=>\s*{/.test(code)
    ) {
      if (/interface\s+\w+|type\s+\w+\s*=|as\s+\w+/.test(code)) {
        return 'typescript';
      }
      return 'javascript';
    }

    // Python 检测
    if (
      /^(def|class|import|from|if __name__|print\()/m.test(trimmedCode) ||
      /^\s*(def|class)\s+\w+/.test(trimmedCode)
    ) {
      return 'python';
    }

    // HTML/XML 检测
    if (/<[^>]+>/.test(trimmedCode) && /<\/[^>]+>/.test(trimmedCode)) {
      return 'markup';
    }

    // CSS 检测
    if (
      /\{[^}]*\}/.test(code) &&
      /[.#]\w+\s*\{|@media|@import|@keyframes/.test(code)
    ) {
      return 'css';
    }

    // JSON 检测
    if (
      /^[\s]*[{\[]/.test(trimmedCode) &&
      /^[\s]*{[\s\S]*}[\s]*$/.test(trimmedCode)
    ) {
      return 'json';
    }

    // Shell/Bash 检测
    if (
      /^#!/.test(trimmedCode) ||
      /^\$\s|echo\s|grep\s|awk\s|sed\s/.test(trimmedCode)
    ) {
      return 'bash';
    }

    // SQL 检测
    if (/^(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)\s/i.test(trimmedCode)) {
      return 'sql';
    }

    // 默认返回 javascript
    return 'javascript';
  }

  /**
   * 高亮代码
   * @param code 要高亮的代码
   * @param language 语言提示（可选）
   * @returns 高亮后的 HTML
   */
  highlightCode(code: string, language?: string): string {
    if (!code.trim()) {
      return `<pre class="language-text"><code>${this.escapeHtml(code)}</code></pre>`;
    }

    try {
      const detectedLanguage = this.detectLanguage(code, language);
      
      // 检查语言是否被 Prism 支持
      if (!Prism.languages[detectedLanguage]) {
        console.warn(`语言 ${detectedLanguage} 不被支持，使用纯文本显示`);
        return `<pre class="language-text"><code>${this.escapeHtml(code)}</code></pre>`;
      }

      const highlightedCode = Prism.highlight(code, Prism.languages[detectedLanguage], detectedLanguage);
      
      return `<pre class="language-${detectedLanguage}"><code class="language-${detectedLanguage}">${highlightedCode}</code></pre>`;
    } catch (error) {
      console.error('代码高亮失败:', error);
      // 降级处理：返回带有基本样式的代码块
      return `<pre class="language-text"><code>${this.escapeHtml(code)}</code></pre>`;
    }
  }

  /**
   * 处理 HTML 中的所有代码块
   * @param html 包含代码块的 HTML
   * @returns 处理后的 HTML
   */
  processCodeBlocks(html: string): string {
    if (!html) return html;

    let processedHtml = html;

    // 处理 <pre><code> 块（通常是代码块）
    const preCodeRegex = /<pre[^>]*><code[^>]*>([\s\S]*?)<\/code><\/pre>/gi;
    const preCodeMatches = Array.from(html.matchAll(preCodeRegex));

    for (const match of preCodeMatches) {
      const [fullMatch, codeContent] = match;
      const decodedCode = this.decodeHtml(codeContent);
      
      if (decodedCode.trim()) {
        const highlightedCode = this.highlightCode(decodedCode);
        processedHtml = processedHtml.replace(fullMatch, highlightedCode);
      }
    }

    // 处理单独的 <code> 标签（内联代码）- 保持简单，不进行高亮
    // 内联代码通常很短，不需要语法高亮

    return processedHtml;
  }

  /**
   * HTML 转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * HTML 解码
   */
  private decodeHtml(html: string): string {
    const div = document.createElement('div');
    div.innerHTML = html;
    return div.textContent || div.innerText || '';
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(): string[] {
    return Object.keys(this.languageMap);
  }
}

// 创建单例实例
export const highlightService = new HighlightService();

// 导出类型
export type { HighlightService };

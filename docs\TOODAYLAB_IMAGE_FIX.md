# ToodayLab 图片显示修复

## 问题描述

用户发现来自 `toodaylab.com`（理想生活实验室）的图片无法正常显示，这些图片有两个特点：

1. **协议相对 URL**：图片 URL 以 `//` 开头，缺少 `http:` 或 `https:` 协议
2. **可能的防盗链**：`files.toodaylab.com` 域名可能有防盗链保护

示例图片 URL：
```html
<img src="//files.toodaylab.com/2025/10/20251027news_20251027023702_00.jpg" />
<img src="//files.toodaylab.com/2025/10/20251027news_20251027023702_01.jpg" />
<img src="//files.toodaylab.com/2025/10/20251027news_20251027023702_02.jpg" />
```

## 解决方案

### 1. 协议处理

在 `ArticleProcessor.processImage()` 方法中添加了协议相对 URL 的处理：

```typescript
// 处理协议相对 URL (以 // 开头)
if (originalSrc.startsWith("//")) {
  originalSrc = "https:" + originalSrc;
  img.src = originalSrc;
}
```

这样可以将 `//files.toodaylab.com/image.jpg` 自动转换为 `https://files.toodaylab.com/image.jpg`。

### 2. 防盗链处理

将 `files.toodaylab.com` 添加到了图片代理域名列表中：

**文件**: `public/preload/modules/image-proxy.ts`
```typescript
const proxyDomains = [
  // ... 其他域名
  "files.toodaylab.com",  // 理想生活实验室
];
```

**文件**: `src/services/utils/imageUtils.ts`
```typescript
const proxyDomains = [
  // ... 其他域名
  "files.toodaylab.com",  // 理想生活实验室
];
```

### 3. 测试支持

在测试工具中添加了 toodaylab.com 图片的测试用例：

```typescript
const TEST_IMAGES = [
  // ... 其他测试图片
  "https://files.toodaylab.com/2025/10/20251027news_20251027023702_00.jpg",
  "//files.toodaylab.com/2025/10/20251027news_20251027023702_01.jpg",
];
```

## 工作原理

1. **协议补全**：当遇到以 `//` 开头的图片 URL 时，自动添加 `https:` 协议
2. **防盗链检测**：识别 `files.toodaylab.com` 域名，标记为需要代理处理
3. **图片代理**：使用 uTools 的 `ubrowser` API 在无 referrer 环境中获取图片
4. **数据转换**：将图片转换为 data URL，彻底绕过防盗链限制

## 测试方法

在浏览器控制台中运行以下命令：

```javascript
// 测试所有图片代理功能（包括 toodaylab.com）
testImageProxy();

// 显示测试图片（可视化验证）
showTestImages();

// 单独测试 toodaylab.com 图片
ImageProxyTester.testSingleImageProxy('https://files.toodaylab.com/2025/10/20251027news_20251027023702_00.jpg');
```

## 预期效果

修复后，toodaylab.com 的图片应该能够：

- ✅ **自动协议补全**：`//files.toodaylab.com/image.jpg` → `https://files.toodaylab.com/image.jpg`
- ✅ **绕过防盗链**：通过图片代理转换为 data URL
- ✅ **正常显示**：在 RSS 文章中正确渲染
- ✅ **缓存优化**：相同图片只处理一次

## 相关文件

- `src/services/content/articleProcessor.ts` - 协议处理逻辑
- `public/preload/modules/image-proxy.ts` - 图片代理服务
- `src/services/utils/imageUtils.ts` - 图片工具函数
- `src/utils/testImageProxy.ts` - 测试工具

## 扩展性

这个解决方案具有很好的扩展性，如果将来遇到其他使用协议相对 URL 或有防盗链的网站，只需要：

1. 将新域名添加到 `proxyDomains` 列表中
2. 在测试用例中添加相应的测试 URL
3. 重新构建项目

协议相对 URL 的处理是通用的，不需要针对特定域名进行配置。

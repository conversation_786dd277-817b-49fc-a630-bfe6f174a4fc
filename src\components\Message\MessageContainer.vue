<template>
  <div class="message-container-wrapper">
    <Message
      v-for="message in messages"
      :key="message.id"
      v-model:visible="message.visible"
      :id="message.id"
      :message="message.message"
      :type="message.type"
      :duration="message.duration"
      :closable="message.closable"
      :position="message.position"
      :on-click="message.onClick"
      :on-close="message.onClose"
      @remove="handleClose"
    />
  </div>
</template>

<script setup lang="ts">
import { messages, useMessage } from "./useMessage";
import Message from "./Message.vue";

const { removeMessage } = useMessage();

const handleClose: (id: string) => void = (id: string) => {
  removeMessage(id);
};
</script>

<style scoped>
.message-container-wrapper {
  pointer-events: none;
}
</style>

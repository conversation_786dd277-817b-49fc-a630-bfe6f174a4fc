/* Prism.js 代码高亮样式 - 适配项目的暗色主题 */

/* 基础代码块样式 */
pre[class*="language-"] {
  background: linear-gradient(135deg, #1f2937, #111827) !important;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 20px;
  margin: 24px 0;
  overflow-x: auto;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  font-family: "Monaco", "Consolas", "Fira Code", "JetBrains Mono", monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  color: #e5e7eb;
}

/* 代码块内的代码样式 */
code[class*="language-"] {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

/* 内联代码样式 */
:not(pre) > code {
  background: rgba(55, 65, 81, 0.8) !important;
  color: #e5e7eb !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-family: "Monaco", "Consolas", "Fira Code", "JetBrains Mono", monospace !important;
  font-size: 0.85em !important;
  border: 1px solid #4b5563 !important;
}

/* 语法高亮颜色 - 基于 GitHub Dark 主题 */
.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
  color: #8b949e;
  font-style: italic;
}

.token.punctuation {
  color: #e6edf3;
}

.token.property,
.token.tag,
.token.boolean,
.token.number,
.token.constant,
.token.symbol,
.token.deleted {
  color: #79c0ff;
}

.token.selector,
.token.attr-name,
.token.string,
.token.char,
.token.builtin,
.token.inserted {
  color: #a5d6ff;
}

.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
  color: #ff7b72;
}

.token.atrule,
.token.attr-value,
.token.keyword {
  color: #ff7b72;
}

.token.function,
.token.class-name {
  color: #d2a8ff;
}

.token.regex,
.token.important,
.token.variable {
  color: #ffa657;
}

.token.important,
.token.bold {
  font-weight: bold;
}

.token.italic {
  font-style: italic;
}

.token.entity {
  cursor: help;
}

/* 特定语言的样式调整 */
.token.namespace {
  opacity: 0.7;
}

/* JavaScript/TypeScript 特定样式 */
.language-javascript .token.keyword,
.language-typescript .token.keyword {
  color: #ff7b72;
}

.language-javascript .token.function,
.language-typescript .token.function {
  color: #d2a8ff;
}

/* Python 特定样式 */
.language-python .token.keyword {
  color: #ff7b72;
}

.language-python .token.function {
  color: #d2a8ff;
}

/* CSS 特定样式 */
.language-css .token.selector {
  color: #79c0ff;
}

.language-css .token.property {
  color: #a5d6ff;
}

/* JSON 特定样式 */
.language-json .token.property {
  color: #79c0ff;
}

.language-json .token.string {
  color: #a5d6ff;
}

/* HTML/XML 特定样式 */
.language-markup .token.tag {
  color: #7ee787;
}

.language-markup .token.attr-name {
  color: #79c0ff;
}

.language-markup .token.attr-value {
  color: #a5d6ff;
}

/* SQL 特定样式 */
.language-sql .token.keyword {
  color: #ff7b72;
  text-transform: uppercase;
}

/* Bash/Shell 特定样式 */
.language-bash .token.function {
  color: #d2a8ff;
}

.language-bash .token.string {
  color: #a5d6ff;
}

/* 行号支持（如果需要的话） */
.line-numbers .line-numbers-rows {
  border-right: 1px solid #4b5563;
  background: rgba(31, 41, 55, 0.5);
}

.line-numbers-rows > span:before {
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  pre[class*="language-"] {
    padding: 16px;
    margin: 16px 0;
    font-size: 0.8rem;
    border-radius: 6px;
  }
}

/* 滚动条样式 */
pre[class*="language-"]::-webkit-scrollbar {
  height: 8px;
}

pre[class*="language-"]::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 4px;
}

pre[class*="language-"]::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

pre[class*="language-"]::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 复制按钮样式（如果需要的话） */
.code-toolbar {
  position: relative;
}

.code-toolbar .toolbar {
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.code-toolbar:hover .toolbar {
  opacity: 1;
}

.code-toolbar .toolbar button {
  background: #374151;
  border: 1px solid #4b5563;
  color: #e5e7eb;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
}

.code-toolbar .toolbar button:hover {
  background: #4b5563;
  border-color: #6b7280;
}

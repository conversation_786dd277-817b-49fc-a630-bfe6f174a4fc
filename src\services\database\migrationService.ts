/**
 * 数据迁移服务
 * 处理数据结构变更和修复数据完整性问题
 */

import { getMainData, updateMainDataSafely } from "./mainDataService";
import { createRSSContent } from "./rssContentService";
import type {
  SubscriptionNode,
  FolderNode,
  ParagraphNode,
} from "@/types/rss/main-data";

/**
 * 修复订阅源ID问题
 * 将使用 undefined feedUrl 的订阅源修复为使用用户输入的URL作为ID
 */
export async function fixSubscriptionIds(): Promise<void> {
  console.log("开始修复订阅源ID问题...");

  try {
    const mainData = await getMainData();
    let hasChanges = false;

    // 递归处理所有节点
    const processNodes = async (
      nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
    ): Promise<void> => {
      for (const node of nodes) {
        if (node.type === "subscription") {
          // 检查订阅源ID是否有问题
          if (!node.id || node.id === "undefined" || node.id === "") {
            console.log(`发现问题订阅源: ${node.name}, 当前ID: ${node.id}`);

            // 使用URL作为新的ID
            if (node.url) {
              const oldId = node.id;
              node.id = node.url;
              hasChanges = true;

              console.log(`修复订阅源ID: ${node.name}, ${oldId} -> ${node.id}`);

              // 尝试迁移RSS内容数据
              await migrateRSSContent(oldId, node.id, node.name);
            } else {
              console.warn(`订阅源 ${node.name} 缺少URL，无法修复`);
            }
          }
        } else if (node.type === "folder") {
          // 递归处理文件夹中的子节点
          await processNodes(node.children);
        }
      }
    };

    await processNodes(mainData.children);

    // 如果有变更，保存主数据
    if (hasChanges) {
      await updateMainDataSafely(async () => {
        return mainData;
      });
      console.log("订阅源ID修复完成");
    } else {
      console.log("未发现需要修复的订阅源ID");
    }
  } catch (error) {
    console.error("修复订阅源ID失败:", error);
    throw error;
  }
}

/**
 * 迁移RSS内容数据
 * 将旧ID的RSS内容迁移到新ID
 */
async function migrateRSSContent(
  oldId: string,
  newId: string,
  sourceName: string
): Promise<void> {
  try {
    // 尝试获取旧的RSS内容
    const oldDocId = `rss-browser/content/${oldId}`;
    const newDocId = `rss-browser/content/${newId}`;

    let oldContent = null;
    try {
      oldContent = await utools.db.get(oldDocId);
    } catch (error: any) {
      if (error.status === 404) {
        console.log(`旧RSS内容不存在: ${oldDocId}`);
        return;
      }
      throw error;
    }

    if (oldContent) {
      // 更新内容中的sourceId
      const updatedContent = {
        ...oldContent,
        _id: newDocId,
        sourceId: newId,
        items:
          oldContent.items?.map((item: any) => ({
            ...item,
            sourceId: newId,
          })) || [],
      };

      // 删除_rev字段，让数据库重新生成
      delete updatedContent._rev;

      // 保存新的RSS内容
      await createRSSContent(updatedContent);
      console.log(`RSS内容迁移成功: ${sourceName}`);

      // 删除旧的RSS内容
      try {
        await utools.db.remove(oldContent);
        console.log(`删除旧RSS内容: ${oldDocId}`);
      } catch (error) {
        console.warn(`删除旧RSS内容失败: ${oldDocId}`, error);
      }
    }
  } catch (error) {
    console.error(`迁移RSS内容失败: ${oldId} -> ${newId}`, error);
  }
}

/**
 * 检查并修复所有数据完整性问题
 */
export async function checkAndFixDataIntegrity(): Promise<void> {
  console.log("开始检查数据完整性...");

  try {
    // 修复订阅源ID问题
    await fixSubscriptionIds();

    // 可以在这里添加其他数据完整性检查
    // await fixOtherIssues();

    console.log("数据完整性检查完成");
  } catch (error) {
    console.error("数据完整性检查失败:", error);
    throw error;
  }
}

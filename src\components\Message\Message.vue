<template>
  <Teleport to="body">
    <Transition
      enter-active-class="message-enter-active"
      leave-active-class="message-leave-active"
      enter-from-class="message-enter-from"
      leave-to-class="message-leave-to"
    >
      <div
        v-if="visible"
        :class="['message-container', `message-${type}`, positionClass]"
        @click="handleClick"
      >
        <div class="message-content">
          <!-- 图标 -->
          <div class="message-icon">
            <component :is="iconComponent" />
          </div>

          <!-- 消息文本 -->
          <div class="message-text">
            {{ message }}
          </div>

          <!-- 关闭按钮 -->
          <button v-if="closable" class="message-close" @click.stop="close">
            <div class="i-material-symbols-close text-base text-gray-200" />
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { computed, onMounted, onUnmounted } from "vue";

export interface MessageProps {
  id: string;
  message: string;
  type?: "success" | "error" | "warning" | "info";
  duration?: number;
  closable?: boolean;
  position?:
    | "top"
    | "top-left"
    | "top-right"
    | "bottom"
    | "bottom-left"
    | "bottom-right";
  onClick?: () => void;
  onClose?: () => void;
}

const props = withDefaults(defineProps<MessageProps>(), {
  type: "info",
  duration: 3000,
  closable: true,
  position: "top",
});

const emit = defineEmits<{
  remove: [id: string];
}>();

const visible = defineModel<boolean>("visible", { default: true });

let timer: NodeJS.Timeout | null = null;

// 位置样式类
const positionClass = computed(() => {
  const positions = {
    top: "message-top",
    "top-left": "message-top-left",
    "top-right": "message-top-right",
    bottom: "message-bottom",
    "bottom-left": "message-bottom-left",
    "bottom-right": "message-bottom-right",
  };
  return positions[props.position];
});

// 图标组件
const iconComponent = computed(() => {
  const icons = {
    success: SuccessIcon,
    error: ErrorIcon,
    warning: WarningIcon,
    info: InfoIcon,
  };
  return icons[props.type];
});

// 自动关闭
const startTimer = () => {
  if (props.duration > 0) {
    timer = setTimeout(() => {
      close();
    }, props.duration);
  }
};

const clearTimer = () => {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
};

const close = () => {
  visible.value = false;
  emit("remove", props.id);
  props.onClose?.();
};

const handleClick = () => {
  props.onClick?.();
};

onMounted(() => {
  startTimer();
});

onUnmounted(() => {
  clearTimer();
});

// 图标组件定义
const SuccessIcon = {
  template: `<div class="i-material-symbols-check-circle-outline text-xl" />`,
};

const ErrorIcon = {
  template: `<div class="i-material-symbols-error-outline text-xl" />`,
};

const WarningIcon = {
  template: `<div class="i-material-symbols-warning-outline text-xl" />`,
};

const InfoIcon = {
  template: `<div class="i-material-symbols-info-outline text-xl" />`,
};
</script>

<style scoped>
/* 消息容器基础样式 - Obsidian 主题适配 */
.message-container {
  position: fixed;
  z-index: 50;
  pointer-events: auto;
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(8px);
  max-width: 24rem;
  min-width: 16rem;
  cursor: pointer;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
}

/* 消息内容 */
.message-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.message-icon {
  flex-shrink: 0;
}

.message-text {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.message-close {
  flex-shrink: 0;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;
  opacity: 0.6;
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
}

.message-close:hover {
  background-color: rgba(255, 255, 255, 0.15);
  opacity: 1;
  transform: scale(1.1);
}

.message-close:active {
  transform: scale(0.95);
}

/* 消息类型样式 - 优雅的 Obsidian 主题适配 */
.message-success {
  background: linear-gradient(135deg, #2a2d2a 0%, #2f332f 100%);
  border: 1px solid #4a5d4a;
  color: #b8e6b8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.message-success .message-icon {
  color: #7dd87d;
}

.message-error {
  background: linear-gradient(135deg, #2d2a2a 0%, #332f2f 100%);
  border: 1px solid #5d4a4a;
  color: #e6b8b8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.message-error .message-icon {
  color: #d87d7d;
}

.message-warning {
  background: linear-gradient(135deg, #2d2c2a 0%, #33312f 100%);
  border: 1px solid #5d5a4a;
  color: #e6d8b8;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.message-warning .message-icon {
  color: #d8c87d;
}

.message-info {
  background: linear-gradient(135deg, #2a2c2d 0%, #2f3133 100%);
  border: 1px solid #4a5a5d;
  color: #b8d8e6;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.08);
}

.message-info .message-icon {
  color: #7dc8d8;
}

/* 位置样式 */
.message-top {
  top: 16px;
  left: 50%;
  transform: translateX(-50%);
}

.message-top-left {
  top: 16px;
  left: 16px;
}

.message-top-right {
  top: 16px;
  right: 16px;
}

.message-bottom {
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
}

.message-bottom-left {
  bottom: 16px;
  left: 16px;
}

.message-bottom-right {
  bottom: 16px;
  right: 16px;
}

/* 动画样式 */
.message-enter-active,
.message-leave-active {
  transition: all 0.3s ease-out;
}

.message-enter-from {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}

.message-leave-to {
  opacity: 0;
  transform: translateY(-8px) scale(0.95);
}
</style>

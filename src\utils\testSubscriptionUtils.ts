/**
 * 订阅源工具函数的简单测试
 * 这个文件可以在浏览器控制台中运行来验证功能
 */

import { generateSubscriptionId, isValidSubscriptionId, cleanUrl, isSameResource } from "./subscriptionUtils";

// 测试函数
export function runSubscriptionUtilsTests() {
  console.log("🧪 开始测试订阅源工具函数...");

  // 测试 generateSubscriptionId
  console.log("\n📋 测试 generateSubscriptionId:");
  
  try {
    // 测试1: feedUrl存在时使用feedUrl
    const test1 = generateSubscriptionId("https://example.com/feed.xml", "https://example.com/rss");
    console.log("✅ 测试1通过:", test1 === "https://example.com/feed.xml");
    
    // 测试2: feedUrl为undefined时使用inputUrl
    const test2 = generateSubscriptionId(undefined, "https://example.com/rss");
    console.log("✅ 测试2通过:", test2 === "https://example.com/rss");
    
    // 测试3: feedUrl为空字符串时使用inputUrl
    const test3 = generateSubscriptionId("", "https://example.com/rss");
    console.log("✅ 测试3通过:", test3 === "https://example.com/rss");
    
    // 测试4: feedUrl只包含空格时使用inputUrl
    const test4 = generateSubscriptionId("   ", "https://example.com/rss");
    console.log("✅ 测试4通过:", test4 === "https://example.com/rss");
    
    // 测试5: 去除feedUrl的前后空格
    const test5 = generateSubscriptionId("  https://example.com/feed.xml  ", "https://example.com/rss");
    console.log("✅ 测试5通过:", test5 === "https://example.com/feed.xml");
    
  } catch (error) {
    console.error("❌ generateSubscriptionId 测试失败:", error);
  }

  // 测试 isValidSubscriptionId
  console.log("\n📋 测试 isValidSubscriptionId:");
  
  try {
    console.log("✅ 有效ID测试通过:", isValidSubscriptionId("https://example.com/feed.xml") === true);
    console.log("✅ 有效ID测试通过:", isValidSubscriptionId("valid-id") === true);
    console.log("✅ 无效ID测试通过:", isValidSubscriptionId(undefined) === false);
    console.log("✅ 无效ID测试通过:", isValidSubscriptionId("") === false);
    console.log("✅ 无效ID测试通过:", isValidSubscriptionId("   ") === false);
    console.log("✅ 无效ID测试通过:", isValidSubscriptionId("undefined") === false);
    console.log("✅ 无效ID测试通过:", isValidSubscriptionId("null") === false);
  } catch (error) {
    console.error("❌ isValidSubscriptionId 测试失败:", error);
  }

  // 测试 cleanUrl
  console.log("\n📋 测试 cleanUrl:");
  
  try {
    console.log("✅ 清理URL测试通过:", cleanUrl("  https://example.com  ") === "https://example.com");
    console.log("✅ 清理URL测试通过:", cleanUrl("https://example.com/path with spaces") === "https://example.com/pathwithspaces");
  } catch (error) {
    console.error("❌ cleanUrl 测试失败:", error);
  }

  // 测试 isSameResource
  console.log("\n📋 测试 isSameResource:");
  
  try {
    const url = "https://example.com/feed.xml";
    console.log("✅ 相同URL测试通过:", isSameResource(url, url) === true);
    console.log("✅ 去除空格URL测试通过:", isSameResource("https://example.com/feed.xml", "  https://example.com/feed.xml  ") === true);
    console.log("✅ 不同URL测试通过:", isSameResource("https://example.com/feed.xml", "https://other.com/feed.xml") === false);
    console.log("✅ 无效URL测试通过:", isSameResource("invalid-url", "invalid-url") === true);
    console.log("✅ 无效URL测试通过:", isSameResource("invalid-url1", "invalid-url2") === false);
  } catch (error) {
    console.error("❌ isSameResource 测试失败:", error);
  }

  // 真实场景测试
  console.log("\n📋 真实场景测试:");
  
  try {
    // 模拟RSS解析器返回feedUrl为undefined的情况
    const mockRSSData1 = {
      title: "测试RSS源",
      description: "测试描述",
      feedUrl: undefined, // 这是问题的核心
      items: []
    };
    
    const userInputUrl1 = "https://example.com/rss";
    const subscriptionId1 = generateSubscriptionId(mockRSSData1.feedUrl, userInputUrl1);
    
    console.log("✅ 场景1测试通过:", subscriptionId1 === userInputUrl1);
    console.log("✅ 场景1ID有效性测试通过:", isValidSubscriptionId(subscriptionId1) === true);

    // 模拟RSS解析器返回有效feedUrl的情况
    const mockRSSData2 = {
      title: "测试RSS源",
      description: "测试描述",
      feedUrl: "https://example.com/actual-feed.xml",
      items: []
    };
    
    const userInputUrl2 = "https://example.com/rss";
    const subscriptionId2 = generateSubscriptionId(mockRSSData2.feedUrl, userInputUrl2);
    
    console.log("✅ 场景2测试通过:", subscriptionId2 === mockRSSData2.feedUrl);
    console.log("✅ 场景2ID有效性测试通过:", isValidSubscriptionId(subscriptionId2) === true);
    
  } catch (error) {
    console.error("❌ 真实场景测试失败:", error);
  }

  console.log("\n🎉 所有测试完成！");
}

// 如果在浏览器环境中，自动运行测试
if (typeof window !== 'undefined') {
  // 延迟执行，确保模块加载完成
  setTimeout(() => {
    console.log("🔧 RSS浏览器 - 订阅源ID修复验证");
    runSubscriptionUtilsTests();
  }, 1000);
}

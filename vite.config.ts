import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import UnoCSS from "unocss/vite";
import path from "path";
import { visualizer } from "rollup-plugin-visualizer";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    UnoCSS(),
    // 打包分析工具（仅在分析模式下启用）
    ...(process.env.ANALYZE
      ? [
          visualizer({
            filename: "dist/stats.html",
            open: true,
            gzipSize: true,
            brotliSize: true,
          }),
        ]
      : []),
  ],
  base: "./",
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  build: {
    outDir: "dist",
    assetsDir: "assets",
    // 启用压缩
    minify: "terser",
    // 生成 source map（可选，生产环境可以关闭）
    sourcemap: false,
    // 设置 chunk 大小警告限制
    chunkSizeWarningLimit: 1000,
    rollupOptions: {
      output: {
        // 手动分割代码块
        manualChunks: {
          // Vue 相关
          "vue-vendor": ["vue", "vue-router"],
          // 状态管理
          "pinia-vendor": ["pinia", "pinia-plugin-persistedstate"],
          // Shiki 代码高亮（单独分块）
          "shiki-vendor": ["shiki"],
          // 其他第三方库
          vendor: ["vue-draggable-plus"],
        },
        // 优化文件名
        chunkFileNames: "assets/js/[name]-[hash].js",
        entryFileNames: "assets/js/[name]-[hash].js",
        assetFileNames: "assets/[ext]/[name]-[hash].[ext]",
      },
    },
    // Terser 压缩选项
    terserOptions: {
      compress: {
        // 移除 console.log
        drop_console: true,
        // 移除 debugger
        drop_debugger: true,
        // 移除未使用的代码
        dead_code: true,
      },
    },
  },
});

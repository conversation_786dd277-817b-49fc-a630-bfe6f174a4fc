/* AddRssModel 组件样式 - Obsidian 深色主题风格 */
.add-rss-model {
  width: 100%;
  max-width: 600px;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, sans-serif;
  color: #e4e4e7;
}

.rss-page {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
}

/* RSS 输入区域 */
.rss-input-section {
  background: transparent;
  border: none;
  border-radius: 0;
  padding: 0;
  transition: all 0.2s ease-in-out;
}

.input-group {
  display: flex;
  gap: 10px;
  align-items: stretch;
}

/* RSS 输入框 */
.rss-input {
  flex: 1;
  padding: 12px 14px;
  background: #383838;
  border: 1px solid #464647;
  border-radius: 6px;
  color: #e4e4e7;
  font-size: 14px;
  font-family: inherit;
  transition: all 0.2s ease-in-out;
  outline: none;
  min-height: 44px;
  box-sizing: border-box;
}

.rss-input::placeholder {
  color: #a1a1aa;
  opacity: 1;
}

.rss-input:focus {
  border-color: #6366f1;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.15);
  background: #404040;
}

.rss-input:hover:not(:focus) {
  border-color: #5a5a5c;
  background: #404040;
}

/* 订阅按钮 */
.subscribe-btn {
  padding: 12px 20px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  min-height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 1px 3px rgba(99, 102, 241, 0.3);
}

.subscribe-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #5855eb 0%, #7c3aed 100%);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
  transform: translateY(-1px);
}

.subscribe-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

.subscribe-btn:disabled {
  background: #4b5563;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
  box-shadow: none;
}

/* 加载状态 */
.loading {
  background: #2d2d30;
  border: 1px solid #464647;
  border-radius: 8px;
  padding: 24px 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #464647;
  border-top: 3px solid #6366f1;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading p {
  margin: 0;
  color: #a1a1aa;
  font-size: 14px;
}

/* 错误状态 */
.error {
  background: linear-gradient(135deg, #2d1b1b 0%, #3d1a1a 100%);
  border: 1px solid #7f1d1d;
  border-radius: 8px;
  padding: 20px;
}

.error-title {
  margin: 0 0 12px 0;
  color: #fca5a5;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-message {
  margin: 0 0 16px 0;
  color: #e4e4e7;
  font-size: 14px;
  line-height: 1.5;
}

.clear-error-btn {
  padding: 10px 20px;
  background: #dc2626;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 13px;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
}

.clear-error-btn:hover {
  background: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

/* 示例 RSS 源区域 */
.rss-examples {
  background: #2d2d30;
  border: 1px solid #464647;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.2s ease-in-out;
}

.rss-examples h3 {
  margin: 0 0 8px 0;
  color: #e4e4e7;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.rss-examples p {
  margin: 0 0 16px 0;
  color: #a1a1aa;
  font-size: 14px;
  line-height: 1.5;
}

.example-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.example-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 14px;
  background: #383838;
  border: 1px solid #464647;
  border-radius: 6px;
  transition: all 0.2s ease-in-out;
  gap: 12px;
}

.example-item:hover {
  background: #404040;
  border-color: #6366f1;
  box-shadow: 0 0 0 1px rgba(99, 102, 241, 0.1);
}

.example-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.example-info strong {
  color: #e4e4e7;
  font-size: 14px;
  font-weight: 500;
}

.example-url {
  color: #a1a1aa;
  font-size: 12px;
  font-family: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas,
    "Courier New", monospace;
  word-break: break-all;
  line-height: 1.4;
}

.use-example-btn {
  padding: 8px 16px;
  background: transparent;
  border: 1px solid #6366f1;
  border-radius: 6px;
  color: #6366f1;
  font-size: 13px;
  font-weight: 500;
  font-family: inherit;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  white-space: nowrap;
  flex-shrink: 0;
}

.use-example-btn:hover {
  background: #6366f1;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.use-example-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(99, 102, 241, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .add-rss-model {
    max-width: 100%;
  }

  .rss-page {
    padding: 16px;
    gap: 14px;
  }

  .loading,
  .error,
  .rss-examples {
    padding: 16px;
  }

  .input-group {
    flex-direction: column;
    gap: 10px;
  }

  .subscribe-btn {
    width: 100%;
    justify-content: center;
  }

  .example-item {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
    padding: 12px;
  }

  .example-info {
    text-align: center;
  }

  .use-example-btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .rss-page {
    padding: 12px;
  }

  .loading,
  .error,
  .rss-examples {
    padding: 14px;
  }

  .rss-input {
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }
}

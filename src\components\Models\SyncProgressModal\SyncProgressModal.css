.loader {
  --clr: #003cff;
  --load-time: 2s;
  outline: 5px solid var(--clr);
  outline-offset: 5px;
  position: relative;
  overflow: hidden;
  border-radius: 10rem;
  width: 10rem;
  padding: 0.5rem 8rem;
}

.loader::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 300%;
  height: 300%;
  background-color: var(--clr);
  z-index: 2;
  animation: loading var(--load-time) ease-in-out infinite;
}

@keyframes loading {
  0% {
    width: 0%;
  }

  100% {
    width: 100%;
  }
}

/* 进度条样式 */
.progress-container {
  width: 100%;
  height: 10px;
  background-color: #374151;
  border-radius: 9999px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #656fac;
  border-radius: 9999px;
  transition: width 0.3s ease-in-out;
}

.sync-source-text {
  white-space: nowrap; /* 防止文本换行 */
  overflow: hidden; /* 隐藏超出容器的文本 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文本 */
  max-width: 100%; /* 确保不超过父容器宽度 */
  display: block; /* 确保元素是块级元素 */
}

/* Details 按钮样式 - 与项目其他按钮保持一致 */
.details-btn {
  padding: 6px 12px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  border-radius: 6px;
  border: 1px solid #464647;
  background-color: #434450;
  color: #e4e4e7;
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  min-width: fit-content;
  white-space: nowrap;
}

/* Details 按钮图标样式 */
.details-btn > div[class*="i-material-symbols"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.details-btn:hover {
  background-color: #4a4b58;
  border-color: #525357;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.details-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/**
 * 文章相关工具函数
 * 用于文章数据处理和标签管理
 */

import { RSSContentItem } from "@/types/rss/rss-content";
import { extractFirstImageFromContent } from "./imageUtils";

/**
 * 获取文章的自定义标签
 * @param article RSS文章对象
 * @returns 自定义标签数组
 */
export const getCustomTags = (article: RSSContentItem): string[] => {
  return (
    article.marks
      ?.filter((mark) => mark.startsWith("#"))
      .map((mark) => mark.substring(1)) || []
  );
};

/**
 * 获取文章封面图片（同步版本，用于模板）
 * @param article RSS文章对象
 * @returns 图片URL或空字符串
 */
export const getArticleImage = (article: RSSContentItem): string => {
  // 使用正则表达式快速提取第一张图片，不使用代理功能
  if (!article.content) return "";

  const imgRegex = /<img[^>]+src="([^"]+)"/i;
  const match = article.content.match(imgRegex);

  if (!match) return "";

  return match[1];
};

/**
 * 获取文章封面图片（异步版本，支持代理）
 * @param article RSS文章对象
 * @returns 图片URL或空字符串
 */
export const getArticleImageAsync = async (
  article: RSSContentItem
): Promise<string> => {
  return await extractFirstImageFromContent(article.content);
};

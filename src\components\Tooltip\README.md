# Tooltip 组件

一个美观、功能丰富的提示框组件，适配 Obsidian 深色主题风格。

## 特性

- 🎨 **多种主题**: 支持 Obsidian、深色、浅色主题
- 📍 **灵活定位**: 12 种不同的显示位置
- 🎯 **多种触发方式**: 悬停、焦点、手动控制
- ⚡ **高性能**: 使用 Teleport 和优化的定位算法
- 🎭 **优雅动画**: 平滑的进入和退出动画
- 📱 **响应式**: 自动边界检测和位置调整
- ♿ **无障碍**: 支持 ARIA 属性和键盘导航
- 🎛️ **高度可定制**: 丰富的配置选项

## 基础用法

```vue
<template>
  <Tooltip content="这是提示信息">
    <button>悬停显示提示</button>
  </Tooltip>
</template>

<script setup>
import { Tooltip } from '@/components/Tooltip'
</script>
```

## API

### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `content` | `string` | - | 提示内容 |
| `placement` | `string` | `'top'` | 显示位置，可选值见下方 |
| `theme` | `string` | `'obsidian'` | 主题样式：`obsidian` \| `dark` \| `light` |
| `trigger` | `string` | `'hover'` | 触发方式：`hover` \| `focus` \| `manual` |
| `showDelay` | `number` | `100` | 显示延迟（毫秒） |
| `hideDelay` | `number` | `100` | 隐藏延迟（毫秒） |
| `showArrow` | `boolean` | `true` | 是否显示箭头 |
| `disabled` | `boolean` | `false` | 是否禁用 |
| `maxWidth` | `string \| number` | `'200px'` | 最大宽度 |
| `zIndex` | `number` | `1000` | z-index 层级 |

### Placement 选项

- `top`, `top-start`, `top-end`
- `bottom`, `bottom-start`, `bottom-end`
- `left`, `left-start`, `left-end`
- `right`, `right-start`, `right-end`

### Events

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| `show` | 提示框显示时触发 | - |
| `hide` | 提示框隐藏时触发 | - |

### Slots

| 插槽名 | 说明 |
|--------|------|
| `default` | 触发元素 |
| `content` | 自定义提示内容 |

### 暴露的方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| `show()` | 手动显示提示框 | - |
| `hide()` | 手动隐藏提示框 | - |
| `visible` | 当前显示状态（只读） | - |

## 使用示例

### 不同位置

```vue
<template>
  <div>
    <Tooltip content="顶部提示" placement="top">
      <button>顶部</button>
    </Tooltip>
    
    <Tooltip content="右侧提示" placement="right">
      <button>右侧</button>
    </Tooltip>
  </div>
</template>
```

### 不同主题

```vue
<template>
  <div>
    <Tooltip content="Obsidian 主题" theme="obsidian">
      <button>Obsidian</button>
    </Tooltip>
    
    <Tooltip content="深色主题" theme="dark">
      <button>深色</button>
    </Tooltip>
    
    <Tooltip content="浅色主题" theme="light">
      <button>浅色</button>
    </Tooltip>
  </div>
</template>
```

### 手动控制

```vue
<template>
  <Tooltip ref="tooltipRef" content="手动控制" trigger="manual">
    <button @click="toggleTooltip">点击控制</button>
  </Tooltip>
</template>

<script setup>
import { ref } from 'vue'

const tooltipRef = ref()

const toggleTooltip = () => {
  if (tooltipRef.value?.visible) {
    tooltipRef.value.hide()
  } else {
    tooltipRef.value.show()
  }
}
</script>
```

### 自定义内容

```vue
<template>
  <Tooltip>
    <template #content>
      <div>
        <strong>自定义标题</strong>
        <p>这里可以放任何内容</p>
      </div>
    </template>
    <button>自定义内容</button>
  </Tooltip>
</template>
```

### 图标按钮应用

```vue
<template>
  <div class="toolbar">
    <Tooltip content="刷新" placement="top">
      <button class="icon-btn">
        <RefreshIcon />
      </button>
    </Tooltip>
    
    <Tooltip content="设置" placement="top">
      <button class="icon-btn">
        <SettingsIcon />
      </button>
    </Tooltip>
  </div>
</template>
```

## 样式定制

组件使用了 CSS 变量和 UnoCSS，可以通过以下方式定制样式：

```css
/* 自定义主题颜色 */
.tooltip-container.tooltip-custom {
  background: linear-gradient(135deg, #your-color 0%, #your-color-2 100%);
  border: 1px solid #your-border-color;
  color: #your-text-color;
}
```

## 注意事项

1. 组件使用 `Teleport` 将提示框渲染到 `body` 元素中，确保正确的层级显示
2. 自动进行边界检测，防止提示框超出视窗范围
3. 支持键盘导航和屏幕阅读器
4. 在组件销毁时会自动清理定时器和事件监听器
5. 建议在图标按钮等小元素上使用，提升用户体验

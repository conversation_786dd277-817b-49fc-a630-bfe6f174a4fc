{"type": "module", "scripts": {"dev": "npm run build:preload && vite", "dev:preload": "cd public/preload && pnpm run dev", "build": "npm run build:preload && vue-tsc && vite build", "build:analyze": "npm run build:preload && vue-tsc && ANALYZE=true vite build", "build:preload": "node scripts/build-preload.js", "build:full": "npm run build:preload && npm run build", "type-check": "vue-tsc --noEmit", "preview": "vite preview", "validate-plugin": "node scripts/validate-plugin.js", "style-guide": "echo 'See scripts/style-migration-guide.md for UnoCSS migration guide'"}, "dependencies": {"pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.5.0", "shiki": "^3.14.0", "vue": "^3.5.13", "vue-draggable-plus": "^0.6.0", "vue-router": "4"}, "devDependencies": {"@iconify-json/material-symbols": "^1.2.39", "@types/node": "^24.5.2", "@unocss/preset-attributify": "^66.5.2", "@unocss/preset-icons": "^66.5.2", "@unocss/preset-uno": "^66.5.2", "@unocss/transformer-directives": "^66.5.2", "@unocss/transformer-variant-group": "^66.5.2", "@vitejs/plugin-vue": "^5.2.1", "typescript": "^5.9.2", "unocss": "^66.5.2", "utools-api-types": "^6.1.0", "vite": "^6.0.11", "vue-tsc": "^3.0.8"}}
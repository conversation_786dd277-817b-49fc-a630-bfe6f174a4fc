<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-6 text-white">Message 组件样式测试</h1>

    <div class="grid grid-cols-2 gap-4 mb-8">
      <button
        class="px-4 py-2 rounded transition-all hover:scale-105"
        style="
          background: linear-gradient(135deg, #2a2d2a 0%, #2f332f 100%);
          color: #b8e6b8;
          border: 1px solid #4a5d4a;
        "
        @click="testSuccess"
      >
        测试成功消息
      </button>

      <button
        class="px-4 py-2 rounded transition-all hover:scale-105"
        style="
          background: linear-gradient(135deg, #2d2a2a 0%, #332f2f 100%);
          color: #e6b8b8;
          border: 1px solid #5d4a4a;
        "
        @click="testError"
      >
        测试错误消息
      </button>

      <button
        class="px-4 py-2 rounded transition-all hover:scale-105"
        style="
          background: linear-gradient(135deg, #2d2c2a 0%, #33312f 100%);
          color: #e6d8b8;
          border: 1px solid #5d5a4a;
        "
        @click="testWarning"
      >
        测试警告消息
      </button>

      <button
        class="px-4 py-2 rounded transition-all hover:scale-105"
        style="
          background: linear-gradient(135deg, #2a2c2d 0%, #2f3133 100%);
          color: #b8d8e6;
          border: 1px solid #4a5a5d;
        "
        @click="testInfo"
      >
        测试信息消息
      </button>
    </div>

    <div class="grid grid-cols-3 gap-4 mb-8">
      <button
        class="px-4 py-2 rounded bg-gray-600 text-white hover:bg-gray-500"
        @click="testTopLeft"
      >
        左上角
      </button>

      <button
        class="px-4 py-2 rounded bg-gray-600 text-white hover:bg-gray-500"
        @click="testTop"
      >
        顶部居中
      </button>

      <button
        class="px-4 py-2 rounded bg-gray-600 text-white hover:bg-gray-500"
        @click="testTopRight"
      >
        右上角
      </button>

      <button
        class="px-4 py-2 rounded bg-gray-600 text-white hover:bg-gray-500"
        @click="testBottomLeft"
      >
        左下角
      </button>

      <button
        class="px-4 py-2 rounded bg-gray-600 text-white hover:bg-gray-500"
        @click="testBottom"
      >
        底部居中
      </button>

      <button
        class="px-4 py-2 rounded bg-gray-600 text-white hover:bg-gray-500"
        @click="testBottomRight"
      >
        右下角
      </button>
    </div>

    <div class="flex gap-4">
      <button
        class="px-4 py-2 rounded bg-purple-600 text-white hover:bg-purple-500"
        @click="testMultiple"
      >
        显示多条消息
      </button>

      <button
        class="px-4 py-2 rounded bg-red-600 text-white hover:bg-red-500"
        @click="clearAll"
      >
        清空所有消息
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { MessageAPI } from "@/components/Message";

const testSuccess = () => {
  MessageAPI.success("RSS 同步完成！共更新了 15 篇文章");
};

const testError = () => {
  MessageAPI.error("RSS 同步失败！请检查网络连接");
};

const testWarning = () => {
  MessageAPI.warning("网络连接不稳定，可能影响同步速度");
};

const testInfo = () => {
  MessageAPI.info("正在同步 RSS 数据，请稍候...");
};

const testTopLeft = () => {
  MessageAPI.info("左上角消息", { position: "top-left" });
};

const testTop = () => {
  MessageAPI.info("顶部居中消息", { position: "top" });
};

const testTopRight = () => {
  MessageAPI.info("右上角消息", { position: "top-right" });
};

const testBottomLeft = () => {
  MessageAPI.info("左下角消息", { position: "bottom-left" });
};

const testBottom = () => {
  MessageAPI.info("底部居中消息", { position: "bottom" });
};

const testBottomRight = () => {
  MessageAPI.info("右下角消息", { position: "bottom-right" });
};

const testMultiple = () => {
  MessageAPI.success("第一条消息", { position: "top-left" });
  setTimeout(() => MessageAPI.warning("第二条消息", { position: "top" }), 300);
  setTimeout(
    () => MessageAPI.info("第三条消息", { position: "top-right" }),
    600
  );
  setTimeout(() => MessageAPI.error("第四条消息", { position: "bottom" }), 900);
};

const clearAll = () => {
  MessageAPI.clear();
};
</script>

<style scoped>
/* 使用与 Layout 相同的背景色 */
div {
  background-color: transparent;
}
</style>

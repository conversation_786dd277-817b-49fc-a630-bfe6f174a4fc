import { defineStore } from "pinia";
import { useRSSDataManager } from "./rss/rssDataManager";
import { useTagManagerStore } from "./rss/tagManager";
import { useArticleContentProcessor } from "./rss/articleContentProcessor";
import { useSyncManager } from "./rss/syncManager";
import { useUIStateManager } from "./rss/uiStateManager";
import { generateSubscriptionId } from "@/utils/subscriptionUtils";
// 节点管理相关导入
import {
  deleteNodeById,
  updateMainDataSafely,
  getMainData,
} from "@/services/database/mainDataService";
import { createRSSContent } from "@/services/database/rssContentService";
import {
  FolderNode,
  SubscriptionNode,
  ParagraphNode,
} from "@/types/rss/main-data";

export const useRSSStore = defineStore("rss", () => {
  const {
    // 状态
    mainData,
    articles,
    loading,
    currentSourceId,
    currentSourceType,

    // 计算属性
    subscriptionCount,
    totalArticles,

    // 操作
    fetchAllData,
    fetchArticles,
    switchToAllArticles,
    switchToSubscription,
    switchToFolder,
    refreshCurrentData,
  } = useRSSDataManager();

  const { hasTag, getTags, toggleTag, getArticlesByTag, getAllTags } =
    useTagManagerStore(mainData, articles);

  const {
    // 段落处理
    currentArticleParagraphs,
    paragraphIdMap,
    setCurrentArticleParagraphs,
    clearCurrentArticleParagraphs,
    parseArticleParagraphs,
    hashCode,
  } = useArticleContentProcessor();

  const {
    // 同步状态
    syncProgress,
    syncTotal,
    syncCurrentSource,
    syncErrors,

    // 同步函数
    syncAllRSSFeeds,
    syncSingleRSSFeed,
    ensureArticleIntegrity,
  } = useSyncManager(loading, fetchAllData);

  const { sidebarActiveButton, setSidebarActiveButton } = useUIStateManager();

  // 节点管理方法 - 直接在 store 中实现
  const renameNode = async (
    id: string,
    type: "folder" | "subscription",
    newName: string
  ) => {
    try {
      // 使用安全更新方式更新主数据
      await updateMainDataSafely(async (mainData) => {
        // 递归查找并更新节点名称
        const updateNodeName = (
          nodes: Array<FolderNode | SubscriptionNode | ParagraphNode>
        ): boolean => {
          for (const node of nodes) {
            // 检查当前节点是否匹配
            if (node.id === id && node.type === type) {
              // 更新名称
              node.name = newName;
              node.lastModifiedDate = new Date().toISOString();
              return true;
            }

            // 如果是文件夹节点，递归检查子节点
            if (node.type === "folder") {
              const foundAndUpdated = updateNodeName(node.children);
              if (foundAndUpdated) {
                return true;
              }
            }
          }
          return false;
        };

        // 从根节点开始查找并更新
        const nodeUpdated = updateNodeName(mainData.children);

        if (!nodeUpdated) {
          throw new Error(
            `未找到要重命名的${type === "folder" ? "文件夹" : "订阅源"}: ${id}`
          );
        }

        return mainData;
      });

      // 如果是订阅源，还需要更新RSS内容中的sourceTitle
      if (type === "subscription") {
        try {
          const docId = `rss-browser/content/${id}`;
          const rssContent = (await utools.db.get(docId)) as any;

          if (rssContent) {
            rssContent.sourceTitle = newName;
            rssContent.lastFetchTime = new Date().toISOString();
            await createRSSContent(rssContent);
          }
        } catch (error) {
          console.error(`更新订阅源内容标题失败: ${id}`, error);
          // 这里不抛出错误，因为主数据已经更新成功
        }
      }

      console.log(
        `成功重命名${
          type === "folder" ? "文件夹" : "订阅源"
        }: ${id} -> ${newName}`
      );

      // 刷新数据
      await refreshCurrentData();
    } catch (error) {
      console.error(
        `重命名${type === "folder" ? "文件夹" : "订阅源"}失败:`,
        error
      );
      throw error;
    }
  };

  const addSubscription = async (rssUrl: string) => {
    try {
      // 使用utoolsAPI获取RSS数据
      const data = await (window as any).utoolsAPI.rss.subscribe(rssUrl);

      // 生成可靠的订阅源ID：优先使用feedUrl，如果不存在则使用用户输入的URL
      const subscriptionId = generateSubscriptionId(data.feedUrl, rssUrl);

      debugger;
      // 使用安全更新方式更新主数据
      await updateMainDataSafely(async (mainData) => {
        // 添加新的订阅源
        mainData.children.push({
          type: "subscription",
          id: subscriptionId,
          url: rssUrl, // 保存用户输入的原始URL
          name: data.title,
          description: data.description,
          favicon: data.favicon,
          lastUpdated: data.lastBuildDate,
          lastFetchTime: new Date().toISOString(),
          unreadCount: data.items.length,
          createdDate: new Date().toISOString(),
          lastModifiedDate: new Date().toISOString(),
        });

        return mainData;
      });

      // 确保每篇文章数据的完整性并添加RSS内容
      const itemsWithIntegrity = data.items.map((item: any) => {
        const articleWithIntegrity = ensureArticleIntegrity(
          item,
          subscriptionId
        );

        // 处理creator字段
        return {
          ...articleWithIntegrity,
          sourceId: subscriptionId,
          creator: item.creator
            ? `${data.title} - ${item.creator}`
            : data.title,
        };
      });

      await createRSSContent({
        _id: `rss-browser/content/${subscriptionId}`,
        sourceId: subscriptionId,
        sourceTitle: data.title,
        lastUpdated: data.lastBuildDate,
        items: itemsWithIntegrity,
        lastFetchTime: new Date().toISOString(),
        fetchCount: 0,
      });

      // 刷新store中的数据
      await fetchAllData();

      return { success: true, data };
    } catch (error) {
      console.error("添加订阅源失败:", error);
      throw error;
    }
  };

  // 删除节点并刷新数据
  const deleteNodeAndRefresh = async (
    id: string,
    type: "folder" | "subscription"
  ) => {
    await deleteNodeById(id, type);

    // 如果删除的是当前选中的源，切换到所有文章
    if (currentSourceId.value === id && currentSourceType.value === type) {
      await switchToAllArticles();
    } else {
      // 否则只刷新主数据
      mainData.value = await getMainData();
      // 重新获取当前源的文章
      await fetchArticles();
    }
  };

  // 在return中添加新方法
  return {
    // 状态
    mainData,
    articles,
    loading,
    currentSourceId,
    currentSourceType,

    // 同步状态
    syncProgress,
    syncTotal,
    syncCurrentSource,
    syncErrors,

    // 计算属性
    subscriptionCount,
    totalArticles,

    // 操作
    fetchAllData,
    fetchArticles,
    switchToAllArticles,
    switchToSubscription,
    switchToFolder,
    deleteNodeAndRefresh,
    refreshCurrentData,
    addSubscription,
    syncAllRSSFeeds,
    syncSingleRSSFeed,
    renameNode,

    // 通用标签功能
    hasTag, // 通用标签检查
    getTags, // 获取指定类型的所有标签
    toggleTag, // 切换标签
    getArticlesByTag, // 查询包含指定标签的所有文章
    getAllTags,

    // 段落处理
    currentArticleParagraphs,
    setCurrentArticleParagraphs,
    clearCurrentArticleParagraphs,
    parseArticleParagraphs,
    paragraphIdMap,
    hashCode,

    // 侧边栏状态
    sidebarActiveButton,
    setSidebarActiveButton,
  };
});

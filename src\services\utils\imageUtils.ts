/**
 * 图片处理相关工具函数
 * 用于图片提取、处理和错误处理
 * 集成了基于 Fluent Reader 经验的图片代理功能
 */

// 图片代理缓存
const imageProxyCache = new Map<string, string>();

/**
 * 检查是否需要使用图片代理
 */
export function needsImageProxy(url: string): boolean {
  if (!url) return false;

  // 已知需要特殊处理的域名（基于 Fluent Reader 的经验扩展）
  const proxyDomains = [
    "image.gcores.com",
    "img.gcores.com",
    "static.gcores.com",
    "cdn.sspai.com",
    "img.sspai.com",
    "pic.36krcnd.com",
    "img.36krcdn.com",
    "inews.gtimg.com",
    "mat1.gtimg.com",
    "p.qpic.cn",
    "mmbiz.qpic.cn",
    "wx1.sinaimg.cn",
    "wx2.sinaimg.cn",
    "wx3.sinaimg.cn",
    "wx4.sinaimg.cn",
    "tva1.sinaimg.cn",
    "tva2.sinaimg.cn",
    "tva3.sinaimg.cn",
    "tva4.sinaimg.cn",
    "img.alicdn.com",
    "gw.alicdn.com",
    "img.t.sinajs.cn",
    "n.sinaimg.cn",
    "k.sinaimg.cn",
    "upload-images.jianshu.io",
    "cdn2.jianshu.io",
    "img.shields.io",
    "files.toodaylab.com", // 理想生活实验室
  ];

  return proxyDomains.some((domain) => url.includes(domain));
}

/**
 * 使用图片代理获取图片
 */
export async function proxyImage(url: string): Promise<string> {
  if (!url) return url;

  // 检查缓存
  if (imageProxyCache.has(url)) {
    return imageProxyCache.get(url)!;
  }

  // 如果不需要代理，直接返回原URL
  if (!needsImageProxy(url)) {
    return url;
  }

  try {
    // 检查是否在 uTools 环境中
    if ((window as any).utoolsAPI && (window as any).utoolsAPI.imageProxy) {
      console.log(`[ImageUtils] 使用 uTools 图片代理: ${url}`);
      const result = await (window as any).utoolsAPI.imageProxy.proxyImage({
        url,
      });

      if (result.success && result.dataUrl) {
        // 缓存结果
        imageProxyCache.set(url, result.dataUrl);
        return result.dataUrl;
      } else {
        console.warn(`[ImageUtils] 图片代理失败: ${result.error}`);
      }
    }

    // 降级处理：返回原URL
    return url;
  } catch (error) {
    console.error(`[ImageUtils] 图片代理异常:`, error);
    return url;
  }
}

/**
 * 从HTML内容中提取第一张图片
 * @param content HTML内容
 * @returns 图片URL或空字符串
 */
export async function extractFirstImageFromContent(
  content: string | undefined
): Promise<string> {
  if (!content) {
    return "";
  }
  // 使用正则表达式匹配img标签的src属性
  const imgRegex = /<img[^>]+src="([^"]+)"/i;
  const match = content.match(imgRegex);

  if (!match) {
    return "";
  }

  let imageUrl = match[1];

  // 尝试使用图片代理
  imageUrl = await proxyImage(imageUrl);

  // 为云存储图片添加时间戳避免缓存问题
  const timestamp = new Date().getTime();

  // 腾讯云COS处理
  if (
    imageUrl.includes("file.myqcloud.com") ||
    imageUrl.includes("qcloud.com")
  ) {
    imageUrl = `${imageUrl}${
      imageUrl.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
  }

  // 阿里云OSS处理（包括使用阿里云OSS的自定义域名）
  if (
    imageUrl.includes("aliyuncs.com") ||
    imageUrl.includes("oss-cn-") ||
    imageUrl.includes("x-oss-process") ||
    imageUrl.includes("image.gcores.com")
  ) {
    imageUrl = `${imageUrl}${
      imageUrl.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
  }

  // AWS S3处理
  if (imageUrl.includes("amazonaws.com") || imageUrl.includes("s3.")) {
    imageUrl = `${imageUrl}${
      imageUrl.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
  }

  // Azure Blob Storage处理
  if (imageUrl.includes("blob.core.windows.net")) {
    imageUrl = `${imageUrl}${
      imageUrl.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
  }

  return imageUrl;
}

/**
 * 处理图片加载错误
 * @param event 图片加载错误事件
 */
export const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;

  // 获取图片的原始src
  const originalSrc = img.getAttribute("data-original-src") || img.src;

  // 检查是否是云存储图片
  const isCloudStorageImage =
    originalSrc.includes("file.myqcloud.com") ||
    originalSrc.includes("qcloud.com") ||
    originalSrc.includes("aliyuncs.com") ||
    originalSrc.includes("oss-cn-") ||
    originalSrc.includes("x-oss-process") ||
    originalSrc.includes("image.gcores.com") ||
    originalSrc.includes("amazonaws.com") ||
    originalSrc.includes("s3.") ||
    originalSrc.includes("blob.core.windows.net");

  if (isCloudStorageImage) {
    // 尝试使用不同的策略重新加载图片

    // 策略1: 添加no-referrer策略
    if (!img.hasAttribute("referrerpolicy")) {
      img.setAttribute("referrerpolicy", "no-referrer");
      // 重新加载图片
      img.src = originalSrc;
      return;
    }

    // 策略2: 添加新的时间戳
    const timestamp = new Date().getTime();
    const newSrc = `${originalSrc}${
      originalSrc.includes("?") ? "&" : "?"
    }_retry=${timestamp}`;
    img.src = newSrc;

    // 如果已经是重试后的URL，则隐藏图片
    if (img.src.includes("_retry=")) {
      img.style.display = "none";
    }
  } else {
    // 非云存储图片，直接隐藏
    img.style.display = "none";
  }
};

/**
 * 为图片元素添加云存储优化属性
 * @param img 图片元素
 */
export const optimizeCloudStorageImage = (img: HTMLImageElement) => {
  const src = img.getAttribute("src");
  if (!src) return;

  // 保存原始src
  img.setAttribute("data-original-src", src);

  // 为云存储图片添加优化属性
  if (
    src.includes("file.myqcloud.com") ||
    src.includes("qcloud.com") ||
    src.includes("aliyuncs.com") ||
    src.includes("oss-cn-") ||
    src.includes("x-oss-process") ||
    src.includes("image.gcores.com")
  ) {
    // 添加no-referrer策略
    img.setAttribute("referrerpolicy", "no-referrer");

    // 添加时间戳避免缓存问题
    const timestamp = new Date().getTime();
    const optimizedSrc = `${src}${
      src.includes("?") ? "&" : "?"
    }_t=${timestamp}`;
    img.setAttribute("src", optimizedSrc);
  }

  // 添加错误处理
  img.addEventListener("error", handleImageError);
};

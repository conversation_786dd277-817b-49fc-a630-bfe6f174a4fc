/* RSS 文章详情页样式 */

.rss-article-page {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.article-header {
  margin-bottom: 20px;
}

.back-btn {
  background: var(--color-bg-secondary);
  border: var(--border-width-card) solid var(--color-border);
  color: var(--color-text-primary);
  padding: 8px 16px;
  border-radius: var(--border-radius-card);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.back-btn:hover {
  background: var(--color-border);
  transform: translateY(-1px);
}

.article-content {
  padding: 30px;
  line-height: 1.6;
}

.article-title {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 20px;
  line-height: 1.3;
  color: var(--color-text-primary);
}

.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: var(--border-width-card) solid var(--color-border);
}

.article-date,
.article-author {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
}

.article-actions {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.action-btn {
  background: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  color: var(--color-text-primary);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: var(--color-border);
  transform: translateY(-1px);
}

.article-categories {
  margin-bottom: 25px;
}

.category-tag {
  display: inline-block;
  background: var(--color-bg-primary);
  color: var(--color-text-secondary);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-right: 8px;
  margin-bottom: 4px;
  border: 1px solid var(--color-border);
}

.article-body {
  line-height: 1.7;
  font-size: 1.05rem;
}

.article-full-content {
  color: var(--color-text-primary);
}

/* 文章内容样式 */
.article-full-content :deep(h1),
.article-full-content :deep(h2),
.article-full-content :deep(h3),
.article-full-content :deep(h4),
.article-full-content :deep(h5),
.article-full-content :deep(h6) {
  color: var(--color-text-primary);
  margin: 25px 0 15px 0;
  font-weight: 600;
}

.article-full-content :deep(h1) { font-size: 1.8rem; }
.article-full-content :deep(h2) { font-size: 1.5rem; }
.article-full-content :deep(h3) { font-size: 1.3rem; }

.article-full-content :deep(p) {
  margin-bottom: 16px;
  color: var(--color-text-primary);
  line-height: 1.7;
}

.article-full-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius-card);
  margin: 20px 0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.article-full-content :deep(a) {
  color: #3b82f6;
  text-decoration: underline;
  transition: color 0.2s ease;
}

.article-full-content :deep(a:hover) {
  color: #1d4ed8;
}

.article-full-content :deep(blockquote) {
  border-left: 4px solid #3b82f6;
  padding-left: 16px;
  margin: 20px 0;
  font-style: italic;
  color: var(--color-text-secondary);
  background: var(--color-bg-primary);
  padding: 16px;
  border-radius: var(--border-radius-card);
}

.article-full-content :deep(code) {
  background: var(--color-bg-primary);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.9em;
  color: var(--color-text-primary);
}

.article-full-content :deep(pre) {
  background: var(--color-bg-primary);
  padding: 16px;
  border-radius: var(--border-radius-card);
  overflow-x: auto;
  margin: 16px 0;
  border: 1px solid var(--color-border);
}

.article-full-content :deep(pre code) {
  background: none;
  padding: 0;
}

.article-full-content :deep(ul),
.article-full-content :deep(ol) {
  margin: 16px 0;
  padding-left: 24px;
}

.article-full-content :deep(li) {
  margin-bottom: 8px;
  color: var(--color-text-primary);
}

.article-snippet,
.article-description {
  font-size: 1.1rem;
  color: var(--color-text-primary);
  line-height: 1.6;
}

.snippet-notice {
  margin-top: 20px;
  padding: 16px;
  background: var(--color-bg-primary);
  border-radius: var(--border-radius-card);
  border-left: 4px solid #3b82f6;
}

.snippet-notice p {
  margin: 0;
  font-size: 0.9rem;
}

.no-content {
  text-align: center;
  padding: 40px 20px;
  color: var(--color-text-secondary);
}

.loading,
.error {
  text-align: center;
  padding: 40px 20px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--color-border);
  border-top: 4px solid var(--color-text-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-title {
  color: #ef4444;
  margin-bottom: 10px;
}

.error-message {
  color: var(--color-text-secondary);
  margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .rss-article-page {
    padding: 10px;
  }
  
  .article-content {
    padding: 20px;
  }
  
  .article-title {
    font-size: 1.5rem;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .article-actions {
    margin-left: 0;
    width: 100%;
  }
  
  .action-btn {
    flex: 1;
    text-align: center;
  }
  
  .article-full-content :deep(h1) { font-size: 1.5rem; }
  .article-full-content :deep(h2) { font-size: 1.3rem; }
  .article-full-content :deep(h3) { font-size: 1.2rem; }
}

@media (max-width: 480px) {
  .article-content {
    padding: 15px;
  }
  
  .article-title {
    font-size: 1.3rem;
  }
  
  .article-body {
    font-size: 1rem;
  }
}

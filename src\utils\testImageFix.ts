/**
 * 测试gcores.com图片修复功能
 * 可以在浏览器控制台中运行来验证图片处理
 */

import {
  extractFirstImageFromContent,
  optimizeCloudStorageImage,
} from "@/services/utils/imageUtils";

// 测试用的HTML内容（包含gcores.com图片）
const testHTML = `
<img src="https://image.gcores.com/a8df047fefd43c2e0f879afb7d71fbd3-460-215.jpg?x-oss-process=image/resize,limit_1,m_fill,w_626,h_292/quality,q_90" />
<p>冥想式挂机游戏《石头模拟器》现已支持多人联机，最多四人。</p>
<div>
  <figure><img src="https://image.gcores.com/f8895be2a61b0ed30dfa404cb8233d35-1200-673.png?x-oss-process=image/resize,limit_1,m_lfit,w_700,h_2000/quality,q_90/watermark,image_d2F0ZXJtYXJrLnBuZw,g_se,x_10,y_10" alt=""></figure>
</div>
`;

export async function testGcoresImageFix() {
  console.log("🧪 开始测试gcores.com图片修复功能...");

  // 测试1: 提取第一张图片
  console.log("\n📋 测试图片提取:");
  const firstImage = await extractFirstImageFromContent(testHTML);
  console.log("提取的第一张图片URL:", firstImage);

  // 检查是否添加了时间戳
  const hasTimestamp = firstImage.includes("_t=");
  console.log("✅ 是否添加时间戳:", hasTimestamp ? "是" : "否");

  // 测试2: 创建图片元素并优化
  console.log("\n📋 测试图片元素优化:");

  // 创建测试图片元素
  const testImg = document.createElement("img");
  testImg.src =
    "https://image.gcores.com/a8df047fefd43c2e0f879afb7d71fbd3-460-215.jpg?x-oss-process=image/resize,limit_1,m_fill,w_626,h_292/quality,q_90";

  console.log("原始图片URL:", testImg.src);

  // 应用优化
  optimizeCloudStorageImage(testImg);

  console.log("优化后图片URL:", testImg.src);
  console.log("referrerpolicy属性:", testImg.getAttribute("referrerpolicy"));
  console.log(
    "data-original-src属性:",
    testImg.getAttribute("data-original-src")
  );

  // 检查优化结果
  const hasReferrerPolicy =
    testImg.getAttribute("referrerpolicy") === "no-referrer";
  const hasOptimizedUrl = testImg.src.includes("_t=");
  const hasOriginalSrc = testImg.hasAttribute("data-original-src");

  console.log("✅ referrerpolicy设置正确:", hasReferrerPolicy ? "是" : "否");
  console.log("✅ URL添加时间戳:", hasOptimizedUrl ? "是" : "否");
  console.log("✅ 保存原始URL:", hasOriginalSrc ? "是" : "否");

  // 测试3: 验证其他阿里云OSS域名仍然工作
  console.log("\n📋 测试其他阿里云OSS域名:");

  const ossUrls = [
    "https://example.oss-cn-beijing.aliyuncs.com/image.jpg",
    "https://cdn.example.com/image.jpg?x-oss-process=image/resize,w_100",
    "https://image.gcores.com/test.jpg?x-oss-process=style/thumb",
  ];

  ossUrls.forEach((url, index) => {
    const img = document.createElement("img");
    img.src = url;
    console.log(`测试URL ${index + 1}:`, url);

    optimizeCloudStorageImage(img);

    const isOptimized =
      img.getAttribute("referrerpolicy") === "no-referrer" &&
      img.src.includes("_t=");
    console.log(`✅ URL ${index + 1} 优化结果:`, isOptimized ? "成功" : "失败");
  });

  // 测试4: 在页面中实际创建图片元素测试
  console.log("\n📋 创建实际图片元素测试:");

  const container = document.createElement("div");
  container.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    width: 200px;
    background: white;
    border: 2px solid #ccc;
    padding: 10px;
    z-index: 9999;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  `;

  const title = document.createElement("h4");
  title.textContent = "gcores.com图片测试";
  title.style.margin = "0 0 10px 0";
  container.appendChild(title);

  const testImage = document.createElement("img");
  testImage.src =
    "https://image.gcores.com/a8df047fefd43c2e0f879afb7d71fbd3-460-215.jpg?x-oss-process=image/resize,limit_1,m_fill,w_626,h_292/quality,q_90";
  testImage.style.cssText = "width: 100%; height: auto; border-radius: 4px;";

  // 应用优化
  optimizeCloudStorageImage(testImage);

  // 添加加载事件监听
  testImage.onload = () => {
    console.log("✅ 图片加载成功！");
    const status = document.createElement("p");
    status.textContent = "✅ 图片加载成功";
    status.style.cssText = "color: green; margin: 5px 0 0 0; font-size: 12px;";
    container.appendChild(status);
  };

  testImage.onerror = () => {
    console.log("❌ 图片加载失败");
    const status = document.createElement("p");
    status.textContent = "❌ 图片加载失败";
    status.style.cssText = "color: red; margin: 5px 0 0 0; font-size: 12px;";
    container.appendChild(status);
  };

  container.appendChild(testImage);

  // 添加关闭按钮
  const closeBtn = document.createElement("button");
  closeBtn.textContent = "关闭";
  closeBtn.style.cssText =
    "margin-top: 10px; padding: 5px 10px; background: #f0f0f0; border: 1px solid #ccc; border-radius: 4px; cursor: pointer;";
  closeBtn.onclick = () => container.remove();
  container.appendChild(closeBtn);

  document.body.appendChild(container);

  console.log("📸 已在页面右上角创建测试图片，请查看是否正常显示");
  console.log("\n🎉 gcores.com图片修复功能测试完成！");
}

// 如果在浏览器环境中，提供全局访问
if (typeof window !== "undefined") {
  (window as any).testGcoresImageFix = testGcoresImageFix;
  console.log(
    "🔧 gcores.com图片修复测试已加载，在控制台运行 testGcoresImageFix() 开始测试"
  );
}

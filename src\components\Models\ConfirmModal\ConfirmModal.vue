<!-- src/components/Models/ConfirmModal/ConfirmModal.vue -->
<template>
  <SimpleModal
    :show="show"
    :card-style="cardStyle"
    :title="title"
    @close="handleClose"
  >
    <div class="confirm-modal">
      <div class="confirm-content">
        <p class="confirm-message">{{ message }}</p>
        <div class="confirm-options">
          <!-- <label class="checkbox-label">
            <input type="checkbox" class="checkbox-input" />
            <span class="checkbox-text">不再提醒</span>
          </label> -->
          <div class="confirm-buttons">
            <button class="btn-cancel" @click="handleCancel">
              {{ cancelText }}
            </button>
            <button class="btn-confirm" @click="handleConfirm">
              {{ confirmText }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </SimpleModal>
</template>

<script setup lang="ts">
import SimpleModal from "../SimpleModal/SimpleModal.vue";

// 定义组件的属性
defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "确认",
  },
  message: {
    type: String,
    default: "您确定要执行此操作吗？",
  },
  confirmText: {
    type: String,
    default: "确认",
  },
  cancelText: {
    type: String,
    default: "取消",
  },
  cardStyle: {
    type: String,
    default: "width: 400px",
  },
  // 预留属性，用于后续实现"不再提醒"功能
  showDontRemind: {
    type: Boolean,
    default: false, // 默认不显示，等待后续功能实现
  },
});

// 定义组件可以触发的事件
const emit = defineEmits(["confirm", "cancel", "close"]);

// 确认操作
const handleConfirm = () => {
  emit("confirm");
};

// 取消操作
const handleCancel = () => {
  emit("cancel");
};

// 关闭模态框
const handleClose = () => {
  emit("close");
};
</script>

<style scoped src="./ConfirmModal.css"></style>

/* <PERSON><PERSON> 代码高亮样式 - 适配项目的暗色主题 */

/* 基础代码块样式 */
.shiki {
  background: linear-gradient(135deg, #1f2937, #111827) !important;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 20px;
  margin: 24px 0;
  overflow-x: auto;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  font-family: "Monaco", "Consolas", "Fira Code", "JetBrains Mono", monospace;
  font-size: 0.9rem;
  line-height: 1.6;
}

/* 代码块内的代码样式 */
.shiki code {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  box-shadow: none !important;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit;
}

/* 内联代码高亮样式 */
.shiki-inline {
  background: linear-gradient(135deg, #1f2937, #111827) !important;
  color: #fbbf24 !important;
  padding: 3px 6px !important;
  border-radius: 4px !important;
  font-family: "Monaco", "<PERSON>solas", "Fira Code", monospace !important;
  font-size: 0.9em !important;
  border: 1px solid #374151 !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3) !important;
  display: inline;
  white-space: nowrap;
}

/* 降级样式（当 Shiki 失败时使用） */
.shiki-fallback {
  background: linear-gradient(135deg, #1f2937, #111827);
  color: #e5e7eb;
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 20px;
  margin: 24px 0;
  overflow-x: auto;
  font-family: "Monaco", "Consolas", "Fira Code", monospace;
  font-size: 0.9rem;
  line-height: 1.6;
}

.shiki-fallback code {
  background: transparent;
  border: none;
  padding: 0;
  color: #fbbf24;
  font-family: inherit;
  font-size: inherit;
}

/* 内容处理中的加载状态样式 */
.content-processing {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.content-processing .loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #374151;
  border-top: 2px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.content-processing p {
  margin: 0;
  font-size: 0.9rem;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shiki {
    padding: 16px;
    margin: 20px 0;
    font-size: 0.85rem;
    border-radius: 6px;
  }
  
  .shiki-inline {
    padding: 2px 4px !important;
    font-size: 0.85em !important;
  }
  
  .shiki-fallback {
    padding: 16px;
    margin: 20px 0;
    font-size: 0.85rem;
  }
}

/* 滚动条样式（适配暗色主题） */
.shiki::-webkit-scrollbar {
  height: 8px;
}

.shiki::-webkit-scrollbar-track {
  background: #1f2937;
  border-radius: 4px;
}

.shiki::-webkit-scrollbar-thumb {
  background: #4b5563;
  border-radius: 4px;
}

.shiki::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}

/* 确保代码高亮与现有样式兼容 */
.article-full-content .shiki {
  /* 覆盖现有的 pre 样式 */
  background: linear-gradient(135deg, #1f2937, #111827) !important;
  border: 1px solid #374151 !important;
  border-radius: 8px !important;
  padding: 20px !important;
  margin: 24px 0 !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.article-full-content .shiki code {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  box-shadow: none !important;
  color: inherit !important;
}

/* 确保内联代码样式正确 */
.article-full-content .shiki-inline {
  background: linear-gradient(135deg, #1f2937, #111827) !important;
  color: #fbbf24 !important;
  padding: 3px 6px !important;
  border-radius: 4px !important;
  font-family: "Monaco", "Consolas", "Fira Code", monospace !important;
  font-size: 0.9em !important;
  border: 1px solid #374151 !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3) !important;
}

/* 优化代码块的可读性 */
.shiki .line {
  min-height: 1.6em;
}

/* 行号样式（如果需要的话） */
.shiki .line-number {
  color: #6b7280;
  margin-right: 16px;
  user-select: none;
}

/* 高亮行样式 */
.shiki .highlighted {
  background-color: rgba(96, 165, 250, 0.1);
  border-left: 3px solid #60a5fa;
  padding-left: 17px;
  margin-left: -20px;
}

/* 确保代码块在不同字体大小下都能正常显示 */
.font-size-small .shiki {
  font-size: 0.8rem;
}

.font-size-medium .shiki {
  font-size: 0.9rem;
}

.font-size-large .shiki {
  font-size: 1rem;
}

.font-size-extra-large .shiki {
  font-size: 1.1rem;
}

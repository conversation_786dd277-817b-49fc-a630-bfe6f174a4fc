/**
 * 订阅源相关工具函数
 */

/**
 * 生成可靠的订阅源ID
 * 优先使用RSS源提供的feedUrl，如果不存在则使用用户输入的URL
 * 
 * @param feedUrl RSS解析器返回的feedUrl（可能为undefined）
 * @param inputUrl 用户输入的RSS URL
 * @returns 可靠的订阅源ID
 */
export function generateSubscriptionId(feedUrl: string | undefined, inputUrl: string): string {
  // 优先使用feedUrl，如果不存在或为空则使用用户输入的URL
  const subscriptionId = feedUrl && feedUrl.trim() ? feedUrl.trim() : inputUrl.trim();
  
  if (!subscriptionId) {
    throw new Error("无法生成订阅源ID：feedUrl和inputUrl都为空");
  }
  
  return subscriptionId;
}

/**
 * 验证订阅源ID是否有效
 * 
 * @param id 订阅源ID
 * @returns 是否有效
 */
export function isValidSubscriptionId(id: string | undefined): boolean {
  return Boolean(id && id.trim() && id !== "undefined" && id !== "null");
}

/**
 * 清理和标准化URL
 * 移除多余的空格和无效字符
 * 
 * @param url 原始URL
 * @returns 清理后的URL
 */
export function cleanUrl(url: string): string {
  return url.trim().replace(/\s+/g, '');
}

/**
 * 检查两个URL是否指向同一个资源
 * 考虑到URL可能有不同的格式但指向同一资源
 * 
 * @param url1 第一个URL
 * @param url2 第二个URL
 * @returns 是否为同一资源
 */
export function isSameResource(url1: string, url2: string): boolean {
  try {
    const cleanUrl1 = cleanUrl(url1);
    const cleanUrl2 = cleanUrl(url2);
    
    // 直接比较
    if (cleanUrl1 === cleanUrl2) {
      return true;
    }
    
    // 尝试解析为URL对象进行比较
    const parsedUrl1 = new URL(cleanUrl1);
    const parsedUrl2 = new URL(cleanUrl2);
    
    // 比较标准化后的URL
    return parsedUrl1.href === parsedUrl2.href;
  } catch (error) {
    // 如果URL解析失败，回退到字符串比较
    return cleanUrl(url1) === cleanUrl(url2);
  }
}

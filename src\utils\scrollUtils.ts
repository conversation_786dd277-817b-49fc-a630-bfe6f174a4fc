/**
 * 滚动相关的工具函数
 */

/**
 * 滚动到页面顶部
 * 使用多重策略确保在不同布局下都能正常工作
 */
export const scrollToTop = () => {
  // 方法1: 尝试滚动到文章标题元素
  const articleTitle = document.querySelector(".article-title");
  if (articleTitle) {
    console.log("Found article title, scrolling to it");
    articleTitle.scrollIntoView({ behavior: "smooth", block: "start" });
    return;
  }

  // 方法2: 尝试滚动到文章容器的第一个元素
  const articleContent = document.querySelector(".article-content");
  if (articleContent && articleContent.firstElementChild) {
    console.log("Found article content, scrolling to first element");
    articleContent.firstElementChild.scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
    return;
  }

  // 方法3: 查找主内容容器并滚动
  const mainContent = document.querySelector(".layout .main-content");
  if (mainContent) {
    mainContent.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    console.log("Scrolling main-content to top");
  } else {
    // 降级到 window 滚动
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
    console.log("Fallback: scrolling window to top");
  }
};

/**
 * 滚动到指定元素
 * @param selector CSS 选择器
 * @param options 滚动选项
 */
export const scrollToElement = (
  selector: string,
  options: ScrollIntoViewOptions = { behavior: "smooth", block: "start" }
) => {
  const element = document.querySelector(selector);
  if (element) {
    console.log(`Scrolling to element: ${selector}`);
    element.scrollIntoView(options);
    return true;
  } else {
    console.warn(`Element not found: ${selector}`);
    return false;
  }
};

/**
 * 获取滚动位置
 * 自动检测正确的滚动容器
 */
export const getScrollPosition = (): number => {
  // 检查主内容容器
  const mainContent = document.querySelector(".layout .main-content") as HTMLElement;
  if (mainContent) {
    return mainContent.scrollTop;
  }
  
  // 降级到 window
  return window.scrollY || document.documentElement.scrollTop;
};

/**
 * 监听滚动事件的工具函数
 * @param callback 滚动回调函数
 * @returns 清理函数
 */
export const addScrollListener = (callback: (scrollY: number) => void) => {
  const handleScroll = (event?: Event) => {
    let scrollY = 0;
    
    // 检查是否是主内容容器的滚动事件
    const target = event?.target as HTMLElement;
    if (target && target.classList.contains("main-content")) {
      scrollY = target.scrollTop;
    } else {
      scrollY = getScrollPosition();
    }
    
    callback(scrollY);
  };

  // 添加事件监听器
  const mainContent = document.querySelector(".layout .main-content");
  if (mainContent) {
    mainContent.addEventListener("scroll", handleScroll);
    console.log("Added scroll listener to main-content");
  } else {
    window.addEventListener("scroll", handleScroll);
    console.log("Fallback: added scroll listener to window");
  }

  // 返回清理函数
  return () => {
    if (mainContent) {
      mainContent.removeEventListener("scroll", handleScroll);
    } else {
      window.removeEventListener("scroll", handleScroll);
    }
  };
};

# gcores.com 图片显示问题修复

## 问题描述

在RSS浏览器中，来自机核网（gcores.com）的图片无法正常显示，具体表现为：

- 图片链接有效，可以直接在浏览器中访问
- 但在RSS浏览器的文章页面中显示为空白或加载失败
- 影响用户阅读体验

## 问题分析

### 根本原因

1. **防盗链限制**：gcores.com 设置了 Referrer 防盗链保护
2. **域名识别缺失**：RSS浏览器的图片处理逻辑未识别 `image.gcores.com` 域名
3. **OSS服务识别**：gcores.com 使用阿里云OSS服务，但使用自定义域名

### 技术细节

gcores.com 的图片URL特征：
```
https://image.gcores.com/xxx.jpg?x-oss-process=image/resize,limit_1,m_fill,w_626,h_292/quality,q_90
```

关键特征：
- 域名：`image.gcores.com`
- 包含阿里云OSS处理参数：`x-oss-process`
- 需要设置 `referrerpolicy="no-referrer"` 绕过防盗链

## 解决方案

### 修改的文件

1. **`src/views/RSSArticle.vue`** - 文章页面图片处理
2. **`src/services/utils/imageUtils.ts`** - 图片工具函数

### 具体修改

#### 1. 扩展阿里云OSS识别规则

**修改前：**
```typescript
// 只识别标准阿里云域名
if (src.includes("aliyuncs.com") || src.includes("oss-cn-")) {
  // 处理逻辑
}
```

**修改后：**
```typescript
// 识别标准域名 + OSS参数 + gcores.com
if (src.includes("aliyuncs.com") || src.includes("oss-cn-") || 
    src.includes("x-oss-process") || src.includes("image.gcores.com")) {
  // 处理逻辑
}
```

#### 2. 应用的优化策略

对识别到的gcores.com图片应用以下优化：

1. **设置referrerpolicy**：`referrerpolicy="no-referrer"`
2. **添加时间戳**：避免缓存问题
3. **错误重试**：加载失败时的重试机制

### 修改详情

#### `src/views/RSSArticle.vue` (第493-505行)
```typescript
// 阿里云OSS处理（包括使用阿里云OSS的自定义域名）
if (
  src.includes("aliyuncs.com") ||
  src.includes("oss-cn-") ||
  src.includes("x-oss-process") ||
  src.includes("image.gcores.com")
) {
  img.setAttribute(
    "src",
    `${src}${src.includes("?") ? "&" : "?"}_t=${timestamp}`
  );
  img.setAttribute("referrerpolicy", "no-referrer");
}
```

#### `src/services/utils/imageUtils.ts` 
- **extractFirstImageFromContent** (第40-50行)
- **handleImageError** (第79-89行) 
- **optimizeCloudStorageImage** (第130-148行)

所有相关函数都添加了对 `x-oss-process` 和 `image.gcores.com` 的识别。

## 验证方法

### 1. 构建验证
```bash
npm run build:preload
```

### 2. 功能测试
在浏览器控制台运行：
```javascript
import { testGcoresImageFix } from '@/utils/testImageFix';
testGcoresImageFix();
```

### 3. 实际测试
1. 打开包含gcores.com图片的RSS文章
2. 检查图片是否正常显示
3. 在开发者工具中查看图片元素的属性

## 预期效果

修复后，gcores.com的图片应该能够：

1. ✅ **正常显示**：绕过防盗链限制
2. ✅ **快速加载**：添加时间戳避免缓存问题  
3. ✅ **错误处理**：加载失败时有重试机制
4. ✅ **兼容性**：不影响其他图片源的正常显示

## 扩展性

这次修复采用了通用的识别策略：

- **OSS参数识别**：通过 `x-oss-process` 识别所有使用阿里云OSS的自定义域名
- **域名白名单**：可以轻松添加更多需要特殊处理的域名
- **统一处理**：所有云存储图片使用相同的优化策略

如果将来遇到其他类似问题，只需要在识别条件中添加相应的域名或参数即可。

## 注意事项

1. **时间戳参数**：为避免缓存问题，会在图片URL后添加时间戳参数
2. **referrerpolicy**：设置为 `no-referrer` 可能影响某些网站的访问统计
3. **兼容性**：修改保持了对现有功能的完全兼容

## 测试用例

修复包含以下测试场景：

- ✅ gcores.com 图片正常显示
- ✅ 标准阿里云OSS图片仍然正常
- ✅ 其他云存储服务不受影响
- ✅ 非云存储图片正常显示
- ✅ 图片加载错误时的重试机制

修复完成后，RSS浏览器应该能够正常显示所有gcores.com的图片内容。

import { ref } from "vue";
import type { MessageProps } from "./Message.vue";

export interface MessageOptions {
  message: string;
  type?: "success" | "error" | "warning" | "info";
  duration?: number;
  closable?: boolean;
  position?:
    | "top"
    | "top-left"
    | "top-right"
    | "bottom"
    | "bottom-left"
    | "bottom-right";
  onClick?: () => void;
  onClose?: () => void;
}

export interface MessageInstance extends MessageProps {
  visible: boolean;
}

// 全局消息列表
const messages = ref<MessageInstance[]>([]);

// 消息ID计数器
let messageId = 0;

// 生成唯一ID
const generateId = () => `message_${++messageId}_${Date.now()}`;

// 创建消息实例
const createMessage = (options: MessageOptions): MessageInstance => {
  const id = generateId();

  return {
    id,
    message: options.message,
    type: options.type || "info",
    duration: options.duration ?? 3000,
    closable: options.closable ?? true,
    position: options.position || "top",
    onClick: options.onClick,
    onClose: options.onClose,
    visible: true,
  };
};

// 添加消息
const addMessage = (options: MessageOptions): string => {
  const messageInstance = createMessage(options);
  messages.value.push(messageInstance);

  // 自动移除消息
  if (messageInstance.duration && messageInstance.duration > 0) {
    setTimeout(() => {
      removeMessage(messageInstance.id);
    }, messageInstance.duration);
  }

  return messageInstance.id;
};

// 移除消息
const removeMessage = (id: string) => {
  const index = messages.value.findIndex((msg) => msg.id === id);
  if (index > -1) {
    messages.value[index].visible = false;
    // 等待动画完成后从数组中移除
    setTimeout(() => {
      const currentIndex = messages.value.findIndex((msg) => msg.id === id);
      if (currentIndex > -1) {
        messages.value.splice(currentIndex, 1);
      }
    }, 300); // 与CSS动画时间保持一致
  }
};

// 清空所有消息
const clearMessages = () => {
  messages.value.forEach((msg) => {
    msg.visible = false;
  });
  setTimeout(() => {
    messages.value.length = 0;
  }, 300);
};

// 消息类型快捷方法
const success = (
  message: string,
  options?: Omit<MessageOptions, "message" | "type">
) => {
  return addMessage({ ...options, message, type: "success" });
};

const error = (
  message: string,
  options?: Omit<MessageOptions, "message" | "type">
) => {
  return addMessage({ ...options, message, type: "error" });
};

const warning = (
  message: string,
  options?: Omit<MessageOptions, "message" | "type">
) => {
  return addMessage({ ...options, message, type: "warning" });
};

const info = (
  message: string,
  options?: Omit<MessageOptions, "message" | "type">
) => {
  return addMessage({ ...options, message, type: "info" });
};

// 主要的 composable 函数
export const useMessage = () => {
  return {
    messages: messages.value,
    addMessage,
    removeMessage,
    clearMessages,
    success,
    error,
    warning,
    info,
  };
};

// 全局消息实例（用于在非组件中调用）
export const Message = {
  success,
  error,
  warning,
  info,
  add: addMessage,
  remove: removeMessage,
  clear: clearMessages,
};

// 导出消息列表供组件使用
export { messages };

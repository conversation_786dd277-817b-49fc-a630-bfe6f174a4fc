/* 使用与Layout一致的Obsidian风格 */
.rss-article-page {
  padding: 0;
  background-color: transparent;
}

.article-header {
  margin-bottom: 24px;
}

.back-btn {
  padding: 8px 12px;
  margin-bottom: 4px;
  margin-right: 4px;
  color: #e5e7eb;
  border-radius: 6px;
  background-color: #434450;
  border: 1px solid #3f414d;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 0.9rem;
  font-family: inherit;
}

.back-btn:hover {
  background-color: #4a4b58;
}

/* 文章内容容器 - 与Layout的content-wrapper一致 */
.article-content {
  background-color: #27282e;
  border: 1.5px solid #3f414d;
  border-radius: 10px;
  padding: 32px;
}

/* 文章标题 */
.article-title {
  color: #e5e7eb;
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 24px;
  line-height: 1.3;
}

/* 文章元信息 */
.article-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #3f414d;
}

.article-date,
.article-author {
  color: #9ca3af;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 文章元信息图标样式 */
.article-date > div[class*="i-material-symbols"],
.article-author > div[class*="i-material-symbols"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  color: #9ca3af;
}

.article-actions {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.action-btn {
  background-color: #434450;
  border: 1px solid #3f414d;
  color: #e5e7eb;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.2s;
  font-family: inherit;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 操作按钮图标样式 */
.action-btn > div[class*="i-material-symbols"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
}

.action-btn:hover {
  background-color: #4a4b58;
}

.action-btn.bookmarked {
  background-color: #f59e0b;
  border-color: #d97706;
  color: #1f2937;
}

.action-btn.bookmarked:hover {
  background-color: #d97706;
}

/* 分类标签 */
.category-tag {
  display: inline-block;
  background-color: #434450;
  color: #e5e7eb;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  margin-right: 8px;
  margin-bottom: 4px;
}

/* 文章正文 */
.article-body {
  line-height: 1.7;
  color: #e5e7eb;
}

.article-full-content {
  font-size: 1.1rem;
  line-height: 1.7;
}

/* 文章内容深度样式 - 优化版 */

/* 标题样式 - 层次分明，视觉引导 */
.article-full-content :deep(h1) {
  color: #f9fafb;
  font-size: 1.875rem;
  font-weight: 700;
  margin: 48px 0 20px 0;
  line-height: 1.3;
  border-bottom: 2px solid #3f414d;
  padding-bottom: 12px;
  position: relative;
}

.article-full-content :deep(h1:first-child) {
  margin-top: 0;
}

.article-full-content :deep(h1::before) {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #60a5fa, #3b82f6);
  border-radius: 1px;
}

.article-full-content :deep(h2) {
  color: #f3f4f6;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 40px 0 16px 0;
  line-height: 1.4;
  position: relative;
  padding-left: 16px;
}

.article-full-content :deep(h2:first-child) {
  margin-top: 0;
}

.article-full-content :deep(h2::before) {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background: linear-gradient(180deg, #60a5fa, #3b82f6);
  border-radius: 2px;
}

.article-full-content :deep(h3) {
  color: #e5e7eb;
  font-size: 1.25rem;
  font-weight: 600;
  margin: 36px 0 12px 0;
  line-height: 1.4;
  position: relative;
  padding-left: 12px;
}

.article-full-content :deep(h3:first-child) {
  margin-top: 0;
}

.article-full-content :deep(h3::before) {
  content: "▶";
  position: absolute;
  left: 0;
  top: 0;
  color: #60a5fa;
  font-size: 0.8em;
  transform: rotate(90deg);
}

.article-full-content :deep(h4),
.article-full-content :deep(h5),
.article-full-content :deep(h6) {
  color: #d1d5db;
  font-weight: 600;
  margin: 32px 0 10px 0;
  line-height: 1.4;
}

.article-full-content :deep(h4:first-child),
.article-full-content :deep(h5:first-child),
.article-full-content :deep(h6:first-child) {
  margin-top: 0;
}

.article-full-content :deep(h4) {
  font-size: 1.125rem;
}

.article-full-content :deep(h5) {
  font-size: 1rem;
}

.article-full-content :deep(h6) {
  font-size: 0.95rem;
  font-weight: 500;
  color: #9ca3af;
}

/* 段落样式 - 优化阅读体验 */
.article-full-content :deep(p) {
  margin-bottom: 18px;
  color: #d1d5db;
  line-height: 1.75;
  text-align: justify;
  text-justify: inter-word;
}

.article-full-content :deep(p:first-of-type) {
  font-size: 1.05em;
  color: #e5e7eb;
  margin-bottom: 24px;
}

/* 链接样式 - 更好的视觉反馈 */
.article-full-content :deep(a) {
  color: #60a5fa;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.article-full-content :deep(a::after) {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, #60a5fa, #3b82f6);
  transition: width 0.3s ease;
}

.article-full-content :deep(a:hover) {
  color: #93c5fd;
}

.article-full-content :deep(a:hover::after) {
  width: 100%;
}

/* 图片样式 - 更好的展示效果 */
.article-full-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 24px auto;
  display: block;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.article-full-content :deep(img:hover) {
  transform: scale(1.02);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
}

/* 引用块样式 - 更加优雅 */
.article-full-content :deep(blockquote) {
  border-left: 4px solid #60a5fa;
  background: linear-gradient(
    90deg,
    rgba(96, 165, 250, 0.1),
    rgba(96, 165, 250, 0.05)
  );
  padding: 20px 24px;
  margin: 24px 0;
  border-radius: 0 8px 8px 0;
  color: #d1d5db;
  font-style: italic;
  position: relative;
  box-shadow: 0 2px 8px rgba(96, 165, 250, 0.1);
}

.article-full-content :deep(blockquote::before) {
  content: '"';
  position: absolute;
  top: -10px;
  left: 16px;
  font-size: 3rem;
  color: #60a5fa;
  opacity: 0.3;
  font-family: serif;
}

.article-full-content :deep(blockquote p) {
  margin-bottom: 12px;
  font-size: 1.05em;
}

.article-full-content :deep(blockquote p:last-child) {
  margin-bottom: 0;
}

/* 代码样式 - 更专业的代码展示 */
.article-full-content :deep(code) {
  background: linear-gradient(135deg, #1f2937, #111827);
  color: #fbbf24;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: "Monaco", "Consolas", "Fira Code", monospace;
  font-size: 0.9em;
  border: 1px solid #374151;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
}

.article-full-content :deep(pre) {
  background: linear-gradient(135deg, #1f2937, #111827);
  border: 1px solid #374151;
  border-radius: 8px;
  padding: 20px;
  margin: 24px 0;
  overflow-x: hidden;
  word-wrap: break-word;
  white-space: pre-wrap;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.article-full-content :deep(pre::before) {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #60a5fa, transparent);
}

.article-full-content :deep(pre code) {
  background: none;
  padding: 0;
  color: #e5e7eb;
  border: none;
  box-shadow: none;
  font-size: 0.95em;
  line-height: 1.6;
}

/* 列表样式 - 更好的视觉层次 */
.article-full-content :deep(ul),
.article-full-content :deep(ol) {
  margin: 20px 0;
  padding-left: 24px;
  color: #d1d5db;
}

.article-full-content :deep(ul) {
  list-style: none;
}

.article-full-content :deep(ul li) {
  position: relative;
  margin-bottom: 8px;
  line-height: 1.7;
  padding-left: 20px;
}

.article-full-content :deep(ul li::before) {
  content: "•";
  position: absolute;
  left: 0;
  color: #60a5fa;
  font-weight: bold;
  font-size: 1.2em;
}

.article-full-content :deep(ol li) {
  margin-bottom: 8px;
  line-height: 1.7;
  padding-left: 8px;
}

.article-full-content :deep(ol li::marker) {
  color: #60a5fa;
  font-weight: 600;
}

/* 嵌套列表样式 */
.article-full-content :deep(ul ul li::before) {
  content: "◦";
  font-size: 1em;
}

.article-full-content :deep(ul ul ul li::before) {
  content: "▪";
  font-size: 0.8em;
}

/* 表格样式 - 现代化表格设计 */
.article-full-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 24px 0;
  background: #27282e;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.article-full-content :deep(th),
.article-full-content :deep(td) {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #3f414d;
}

.article-full-content :deep(th) {
  background: linear-gradient(135deg, #374151, #4b5563);
  color: #f9fafb;
  font-weight: 600;
  font-size: 0.95em;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.article-full-content :deep(td) {
  color: #d1d5db;
}

.article-full-content :deep(tr:hover td) {
  background: rgba(96, 165, 250, 0.05);
}

.article-full-content :deep(tr:last-child td) {
  border-bottom: none;
}

/* 分隔线样式 */
.article-full-content :deep(hr) {
  border: none;
  height: 2px;
  background: linear-gradient(90deg, transparent, #3f414d, transparent);
  margin: 32px 0;
  position: relative;
}

.article-full-content :deep(hr::after) {
  content: "❋";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: #27282e;
  color: #60a5fa;
  padding: 0 12px;
  font-size: 1.2em;
}

/* 强调和斜体样式 */
.article-full-content :deep(strong),
.article-full-content :deep(b) {
  color: #f9fafb;
  font-weight: 700;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.article-full-content :deep(em),
.article-full-content :deep(i) {
  color: #e5e7eb;
  font-style: italic;
  position: relative;
}

.article-full-content :deep(mark) {
  background: linear-gradient(
    120deg,
    rgba(251, 191, 36, 0.3),
    rgba(245, 158, 11, 0.3)
  );
  color: #fbbf24;
  padding: 2px 4px;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(251, 191, 36, 0.2);
}

/* 删除线和下划线 */
.article-full-content :deep(del),
.article-full-content :deep(s) {
  color: #9ca3af;
  text-decoration: line-through;
  opacity: 0.7;
}

.article-full-content :deep(u) {
  text-decoration: underline;
  text-decoration-color: #60a5fa;
  text-underline-offset: 3px;
}

/* 上标和下标 */
.article-full-content :deep(sup),
.article-full-content :deep(sub) {
  font-size: 0.75em;
  color: #9ca3af;
}

/* 键盘按键样式 */
.article-full-content :deep(kbd) {
  background: linear-gradient(135deg, #374151, #4b5563);
  color: #f9fafb;
  padding: 3px 6px;
  border-radius: 4px;
  font-family: "Monaco", "Consolas", monospace;
  font-size: 0.85em;
  border: 1px solid #6b7280;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.5);
}

/* 缩写样式 */
.article-full-content :deep(abbr) {
  color: #60a5fa;
  text-decoration: underline dotted;
  text-underline-offset: 3px;
  cursor: help;
}

/* 引用样式 */
.article-full-content :deep(cite) {
  color: #9ca3af;
  font-style: italic;
}

.article-full-content :deep(q) {
  color: #d1d5db;
  font-style: italic;
}

.article-full-content :deep(q::before) {
  content: '"';
  color: #60a5fa;
}

.article-full-content :deep(q::after) {
  content: '"';
  color: #60a5fa;
}

/* 定义列表样式 */
.article-full-content :deep(dl) {
  margin: 20px 0;
}

.article-full-content :deep(dt) {
  color: #f9fafb;
  font-weight: 600;
  margin-top: 16px;
  margin-bottom: 4px;
}

.article-full-content :deep(dd) {
  color: #d1d5db;
  margin-left: 20px;
  margin-bottom: 8px;
  padding-left: 16px;
  border-left: 2px solid #3f414d;
}

/* 地址和联系信息样式 */
.article-full-content :deep(address) {
  color: #9ca3af;
  font-style: italic;
  margin: 16px 0;
  padding: 12px 16px;
  background: rgba(156, 163, 175, 0.1);
  border-radius: 6px;
  border-left: 3px solid #9ca3af;
}

/* 详情和摘要样式 */
.article-full-content :deep(details) {
  margin: 20px 0;
  border: 1px solid #3f414d;
  border-radius: 8px;
  overflow: hidden;
}

.article-full-content :deep(summary) {
  background: linear-gradient(135deg, #374151, #4b5563);
  color: #f9fafb;
  padding: 12px 16px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.2s ease;
}

.article-full-content :deep(summary:hover) {
  background: linear-gradient(135deg, #4b5563, #6b7280);
}

.article-full-content :deep(details[open] summary) {
  border-bottom: 1px solid #3f414d;
}

.article-full-content :deep(details > *:not(summary)) {
  padding: 16px;
}

/* 图片说明样式 */
.article-full-content :deep(figure) {
  margin: 24px 0;
  text-align: center;
}

.article-full-content :deep(figcaption) {
  color: #9ca3af;
  font-size: 0.9em;
  font-style: italic;
  margin-top: 8px;
  padding: 0 16px;
}

/* 特殊内容样式 */
.article-full-content :deep(.highlight),
.article-full-content :deep(.callout) {
  background: linear-gradient(
    135deg,
    rgba(96, 165, 250, 0.1),
    rgba(59, 130, 246, 0.05)
  );
  border: 1px solid rgba(96, 165, 250, 0.2);
  border-radius: 8px;
  padding: 16px 20px;
  margin: 20px 0;
  position: relative;
}

.article-full-content :deep(.highlight::before),
.article-full-content :deep(.callout::before) {
  content: "💡";
  position: absolute;
  top: 16px;
  left: 20px;
  font-size: 1.2em;
}

.article-full-content :deep(.highlight p),
.article-full-content :deep(.callout p) {
  margin-left: 32px;
  margin-bottom: 8px;
}

/* 警告和提示框样式 */
.article-full-content :deep(.warning) {
  background: linear-gradient(
    135deg,
    rgba(245, 158, 11, 0.1),
    rgba(217, 119, 6, 0.05)
  );
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 8px;
  padding: 16px 20px;
  margin: 20px 0;
  position: relative;
}

.article-full-content :deep(.warning::before) {
  content: "⚠️";
  position: absolute;
  top: 16px;
  left: 20px;
  font-size: 1.2em;
}

.article-full-content :deep(.error) {
  background: linear-gradient(
    135deg,
    rgba(239, 68, 68, 0.1),
    rgba(220, 38, 38, 0.05)
  );
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 16px 20px;
  margin: 20px 0;
  position: relative;
}

.article-full-content :deep(.error::before) {
  content: "❌";
  position: absolute;
  top: 16px;
  left: 20px;
  font-size: 1.2em;
}

.article-full-content :deep(.success) {
  background: linear-gradient(
    135deg,
    rgba(34, 197, 94, 0.1),
    rgba(22, 163, 74, 0.05)
  );
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 8px;
  padding: 16px 20px;
  margin: 20px 0;
  position: relative;
}

.article-full-content :deep(.success::before) {
  content: "✅";
  position: absolute;
  top: 16px;
  left: 20px;
  font-size: 1.2em;
}

/* 响应式优化 - 移动端标签样式调整 */
@media (max-width: 768px) {
  .article-full-content :deep(h1) {
    font-size: 1.5rem;
    margin: 36px 0 16px 0;
  }

  .article-full-content :deep(h1:first-child) {
    margin-top: 0;
  }

  .article-full-content :deep(h2) {
    font-size: 1.25rem;
    margin: 32px 0 12px 0;
  }

  .article-full-content :deep(h2:first-child) {
    margin-top: 0;
  }

  .article-full-content :deep(h3) {
    font-size: 1.125rem;
    margin: 28px 0 10px 0;
  }

  .article-full-content :deep(h3:first-child) {
    margin-top: 0;
  }

  .article-full-content :deep(blockquote) {
    padding: 16px 20px;
    margin: 20px 0;
  }

  .article-full-content :deep(blockquote::before) {
    font-size: 2.5rem;
    top: -8px;
    left: 12px;
  }

  .article-full-content :deep(pre) {
    padding: 16px;
    margin: 20px 0;
    font-size: 0.9em;
  }

  .article-full-content :deep(table) {
    font-size: 0.9em;
  }

  .article-full-content :deep(th),
  .article-full-content :deep(td) {
    padding: 8px 12px;
  }

  .article-full-content :deep(.highlight),
  .article-full-content :deep(.callout),
  .article-full-content :deep(.warning),
  .article-full-content :deep(.error),
  .article-full-content :deep(.success) {
    padding: 12px 16px;
    margin: 16px 0;
  }

  .article-full-content :deep(.highlight p),
  .article-full-content :deep(.callout p) {
    margin-left: 28px;
  }
}

@media (max-width: 480px) {
  .article-full-content :deep(h1) {
    font-size: 1.375rem;
    margin: 32px 0 14px 0;
  }

  .article-full-content :deep(h1:first-child) {
    margin-top: 0;
  }

  .article-full-content :deep(h2) {
    font-size: 1.125rem;
    margin: 28px 0 10px 0;
  }

  .article-full-content :deep(h2:first-child) {
    margin-top: 0;
  }

  .article-full-content :deep(h3) {
    font-size: 1rem;
    margin: 24px 0 8px 0;
  }

  .article-full-content :deep(h3:first-child) {
    margin-top: 0;
  }

  .article-full-content :deep(ul),
  .article-full-content :deep(ol) {
    padding-left: 20px;
  }

  .article-full-content :deep(blockquote) {
    padding: 12px 16px;
  }

  .article-full-content :deep(pre) {
    padding: 12px;
    font-size: 0.85em;
  }

  .article-full-content :deep(table) {
    font-size: 0.85em;
  }

  .article-full-content :deep(th),
  .article-full-content :deep(td) {
    padding: 6px 8px;
  }
}

/* 摘要提示 */
.snippet-notice {
  margin-top: 24px;
  padding: 16px;
  background-color: rgba(96, 165, 250, 0.1);
  border-radius: 8px;
  border-left: 3px solid #60a5fa;
}

.snippet-notice p {
  margin: 0;
  color: #9ca3af;
  font-style: italic;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 提示信息图标样式 */
.snippet-notice p > div[class*="i-material-symbols"],
.snippet-text > div[class*="i-material-symbols"] {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  color: #60a5fa;
}

/* 修复后的提示文本样式 */
.snippet-text {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0;
  font-style: italic;
}

/* 无内容、加载和错误状态 */
.no-content,
.loading,
.error {
  text-align: center;
  padding: 40px 20px;
  color: #9ca3af;
}

/* 错误标题样式 */
.error-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: #ef4444;
  margin-bottom: 16px;
}

.error-title > div[class*="i-material-symbols"] {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  color: #ef4444;
}

/* 无内容状态图标样式 */
.no-content p > div[class*="i-material-symbols"],
.no-content-text > div[class*="i-material-symbols"] {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  color: #9ca3af;
  display: inline-block;
  margin-right: 6px;
}

/* 修复后的无内容文本样式 */
.no-content-text {
  display: flex;
  align-items: center;
  gap: 6px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #3f414d;
  border-top: 3px solid #60a5fa;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 字体大小类 */
.font-size-small .article-title {
  font-size: 1.75rem;
}

.font-size-small .article-full-content {
  font-size: 1rem;
}

.font-size-medium .article-title {
  font-size: 2rem;
}

.font-size-medium .article-full-content {
  font-size: 1.1rem;
}

.font-size-large .article-title {
  font-size: 2.25rem;
}

.font-size-large .article-full-content {
  font-size: 1.2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .article-content {
    padding: 20px;
  }

  .article-title {
    font-size: 1.5rem;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .article-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }

  .action-btn {
    flex: 1;
    text-align: center;
  }

  .font-size-small .article-title {
    font-size: 1.25rem;
  }

  .font-size-medium .article-title {
    font-size: 1.5rem;
  }

  .font-size-large .article-title {
    font-size: 1.75rem;
  }
}

@media (max-width: 480px) {
  .article-content {
    padding: 16px;
  }

  .back-btn {
    width: 100%;
    text-align: center;
  }

  .article-actions {
    flex-direction: column;
    gap: 6px;
  }
}

/* ==================== 回到顶部按钮 ==================== */
.back-to-top-btn {
  position: fixed !important;
  bottom: 24px !important;
  right: 24px !important;
  width: 48px !important;
  height: 48px !important;
  background-color: #434450 !important;
  border: 1px solid #464647 !important;
  border-radius: 50% !important;
  color: #e4e4e7 !important;
  cursor: pointer !important;
  transition: all 0.3s ease-in-out !important;
  z-index: 9999 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-family: inherit !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

/* 回到顶部按钮图标 */
.back-to-top-btn > div[class*="i-material-symbols"] {
  width: 24px;
  height: 24px;
  flex-shrink: 0;
}

.back-to-top-btn:hover {
  background-color: #4a4b58;
  border-color: #525357;
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
}

.back-to-top-btn:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 响应式设计 - 在小屏幕上调整位置 */
@media (max-width: 768px) {
  .back-to-top-btn {
    bottom: 16px;
    right: 16px;
    width: 44px;
    height: 44px;
  }

  .back-to-top-btn > div[class*="i-material-symbols"] {
    width: 20px;
    height: 20px;
  }
}

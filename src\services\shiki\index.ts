import {
  createHighlighter,
  type Highlighter,
  type BundledLanguage,
  type BundledTheme,
} from "shiki";

/**
 * <PERSON>ki 代码高亮服务
 * 提供代码语法高亮功能，支持多种编程语言和主题
 */
class ShikiService {
  private highlighter: Highlighter | null = null;
  private isInitialized = false;
  private initPromise: Promise<void> | null = null;

  // 支持的编程语言列表（精简版 - 只保留最常用的语言）
  private readonly supportedLanguages: BundledLanguage[] = [
    "javascript",
    "typescript",
    "python",
    "java",
    "html",
    "css",
    "json",
    "xml",
    "yaml",
    "bash",
    "sql",
    "markdown",
    "vue",
    "jsx",
    "tsx",
  ];

  // 主题配置 - 使用适合暗色主题的配色
  private readonly theme: BundledTheme = "github-dark";

  /**
   * 初始化 Shiki 高亮器
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    if (this.initPromise) {
      return this.initPromise;
    }

    this.initPromise = this._doInitialize();
    return this.initPromise;
  }

  private async _doInitialize(): Promise<void> {
    try {
      console.log("正在初始化 Shiki 代码高亮器...");

      this.highlighter = await createHighlighter({
        themes: [this.theme],
        langs: this.supportedLanguages,
      });

      this.isInitialized = true;
      console.log("Shiki 代码高亮器初始化完成");
    } catch (error) {
      console.error("Shiki 初始化失败:", error);
      throw error;
    }
  }

  /**
   * 检测代码语言
   * 基于代码内容和可能的语言提示进行智能检测
   */
  private detectLanguage(code: string, hint?: string): BundledLanguage {
    // 如果有明确的语言提示，优先使用
    if (hint && this.supportedLanguages.includes(hint as BundledLanguage)) {
      return hint as BundledLanguage;
    }

    // 基于代码内容进行简单的语言检测
    const trimmedCode = code.trim();

    // JavaScript/TypeScript 检测
    if (
      /^(import|export|const|let|var|function|class|interface|type)\s/.test(
        trimmedCode
      ) ||
      /\.(js|ts|jsx|tsx)$/.test(hint || "") ||
      /console\.(log|error|warn)/.test(code) ||
      /=>\s*{/.test(code)
    ) {
      if (/interface\s+\w+|type\s+\w+\s*=|as\s+\w+/.test(code)) {
        return "typescript";
      }
      return "javascript";
    }

    // Python 检测
    if (
      /^(def|class|import|from|if __name__|print\(|range\()/.test(
        trimmedCode
      ) ||
      /\.py$/.test(hint || "") ||
      /:\s*$/.test(trimmedCode.split("\n")[0])
    ) {
      return "python";
    }

    // Java 检测
    if (
      /^(public|private|protected|class|interface|package|import)/.test(
        trimmedCode
      ) ||
      /\.java$/.test(hint || "") ||
      /System\.out\.println/.test(code)
    ) {
      return "java";
    }

    // C/C++ 检测
    if (
      /^#include|^#define|^#ifdef/.test(trimmedCode) ||
      /\.(c|cpp|h|hpp)$/.test(hint || "") ||
      /std::|cout|cin|printf|scanf/.test(code)
    ) {
      return /std::|cout|cin|class|template/.test(code) ? "cpp" : "c";
    }

    // HTML 检测
    if (
      /^<!DOCTYPE|^<html|^<\w+/.test(trimmedCode) ||
      /\.html?$/.test(hint || "") ||
      /<\/\w+>/.test(code)
    ) {
      return "html";
    }

    // CSS 检测
    if (
      /\{[^}]*\}/.test(code) &&
      (/\.(css|scss|sass|less)$/.test(hint || "") ||
        /[.#]\w+\s*\{|@media|@import|@keyframes/.test(code))
    ) {
      if (/\$\w+|@mixin|@include/.test(code)) return "scss";
      if (/@\w+:/.test(code)) return "less";
      return "css";
    }

    // JSON 检测
    if (
      (/^[\s]*[{\[]/.test(trimmedCode) && /\.json$/.test(hint || "")) ||
      /^[\s]*{[\s\S]*}[\s]*$/.test(trimmedCode)
    ) {
      return "json";
    }

    // Shell/Bash 检测
    if (
      /^#!/.test(trimmedCode) ||
      /\.(sh|bash)$/.test(hint || "") ||
      /^\$\s|echo\s|grep\s|awk\s|sed\s/.test(trimmedCode)
    ) {
      return "bash";
    }

    // SQL 检测
    if (
      /^(SELECT|INSERT|UPDATE|DELETE|CREATE|ALTER|DROP)\s/i.test(trimmedCode) ||
      /\.sql$/.test(hint || "")
    ) {
      return "sql";
    }

    // XML 检测
    if (/^<\?xml|^<\w+/.test(trimmedCode) && /\.xml$/.test(hint || "")) {
      return "xml";
    }

    // 默认返回 JavaScript（最常用）
    return "javascript";
  }

  /**
   * 高亮代码
   * @param code 要高亮的代码
   * @param language 语言提示（可选）
   * @returns 高亮后的 HTML
   */
  async highlightCode(code: string, language?: string): Promise<string> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    if (!this.highlighter) {
      console.warn("Shiki 高亮器未初始化，返回原始代码");
      return `<pre><code>${this.escapeHtml(code)}</code></pre>`;
    }

    try {
      const detectedLanguage = this.detectLanguage(code, language);

      const html = this.highlighter.codeToHtml(code, {
        lang: detectedLanguage,
        theme: this.theme,
      });

      return html;
    } catch (error) {
      console.error("代码高亮失败:", error);
      // 降级处理：返回带有基本样式的代码块
      return `<pre class="shiki-fallback"><code>${this.escapeHtml(
        code
      )}</code></pre>`;
    }
  }

  /**
   * 处理 HTML 中的所有代码块
   * @param html 包含代码块的 HTML
   * @returns 处理后的 HTML
   */
  async processCodeBlocks(html: string): Promise<string> {
    if (!html) return html;

    let processedHtml = html;

    // 处理 <pre><code> 块（通常是代码块）
    const preCodeRegex = /<pre[^>]*><code[^>]*>([\s\S]*?)<\/code><\/pre>/gi;
    const preCodeMatches = Array.from(html.matchAll(preCodeRegex));

    for (const match of preCodeMatches) {
      const [fullMatch, codeContent] = match;
      const decodedCode = this.decodeHtml(codeContent);

      if (decodedCode.trim()) {
        const highlightedCode = await this.highlightCode(decodedCode);
        processedHtml = processedHtml.replace(fullMatch, highlightedCode);
      }
    }

    // 处理单独的 <code> 标签（内联代码）
    const inlineCodeRegex = /<code[^>]*>([^<]+)<\/code>/gi;
    const inlineMatches = Array.from(processedHtml.matchAll(inlineCodeRegex));

    for (const match of inlineMatches) {
      const [fullMatch, codeContent] = match;
      const decodedCode = this.decodeHtml(codeContent);

      // 只对较长的内联代码进行高亮（避免对简单的变量名等进行高亮）
      if (decodedCode.trim().length > 10 || /[{}();]/.test(decodedCode)) {
        const highlightedCode = await this.highlightCode(decodedCode);
        // 将块级代码转换为内联样式
        const inlineHighlighted = highlightedCode
          .replace(/<pre[^>]*>/, '<code class="shiki-inline">')
          .replace(/<\/pre>/, "</code>")
          .replace(/<code[^>]*>/, "")
          .replace(/<\/code>/, "");

        processedHtml = processedHtml.replace(
          fullMatch,
          `<code class="shiki-inline">${inlineHighlighted}</code>`
        );
      }
    }

    return processedHtml;
  }

  /**
   * HTML 转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * HTML 解码
   */
  private decodeHtml(html: string): string {
    const div = document.createElement("div");
    div.innerHTML = html;
    return div.textContent || div.innerText || "";
  }

  /**
   * 获取支持的语言列表
   */
  getSupportedLanguages(): BundledLanguage[] {
    return [...this.supportedLanguages];
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}

// 创建单例实例
export const shikiService = new ShikiService();

// 导出类型
export type { BundledLanguage, BundledTheme };

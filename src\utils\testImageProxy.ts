/**
 * 图片代理功能测试工具
 * 用于在浏览器控制台中测试新的图片处理功能
 */

import { needsImageProxy, proxyImage } from "@/services/utils/imageUtils";
import { ArticleProcessor } from "@/services/content/articleProcessor";

// 测试用的图片URL
const TEST_IMAGES = [
  "https://image.gcores.com/a8df047fefd43c2e0f879afb7d71fbd3-460-215.jpg?x-oss-process=image/resize,limit_1,m_fill,w_626,h_292/quality,q_90",
  "https://img.gcores.com/f8895be2a61b0ed30dfa404cb8233d35-1200-673.png",
  "https://cdn.sspai.com/2023/12/15/article_cover_image_1702627200000.jpg",
  "https://pic.36krcnd.com/202312/15/v2_example.jpg",
  "https://wx1.sinaimg.cn/large/example.jpg",
  "https://upload-images.jianshu.io/upload_images/example.png",
  "https://files.toodaylab.com/2025/10/20251027news_20251027023702_00.jpg",
  "//files.toodaylab.com/2025/10/20251027news_20251027023702_01.jpg",
];

// 测试用的HTML内容
const TEST_HTML_CONTENT = `
<div>
  <h1>测试文章标题</h1>
  <p>这是一个测试段落。</p>
  <img src="https://image.gcores.com/a8df047fefd43c2e0f879afb7d71fbd3-460-215.jpg?x-oss-process=image/resize,limit_1,m_fill,w_626,h_292/quality,q_90" alt="测试图片1" />
  <p>另一个段落。</p>
  <img src="https://cdn.sspai.com/2023/12/15/article_cover_image_1702627200000.jpg" alt="测试图片2" />
  <p>测试协议相对URL：</p>
  <img src="//files.toodaylab.com/2025/10/20251027news_20251027023702_00.jpg" alt="toodaylab图片1" />
  <img src="//files.toodaylab.com/2025/10/20251027news_20251027023702_01.jpg" alt="toodaylab图片2" />
  <script>alert('这个脚本应该被移除');</script>
  <a href="https://example.com" onclick="alert('危险代码')">测试链接</a>
</div>
`;

export class ImageProxyTester {
  /**
   * 测试图片代理检测功能
   */
  static testImageProxyDetection(): void {
    console.group("🔍 测试图片代理检测功能");

    TEST_IMAGES.forEach((url, index) => {
      const needsProxy = needsImageProxy(url);
      console.log(`${index + 1}. ${url}`);
      console.log(`   需要代理: ${needsProxy ? "✅ 是" : "❌ 否"}`);
      console.log("");
    });

    console.groupEnd();
  }

  /**
   * 测试单个图片代理功能
   */
  static async testSingleImageProxy(url: string): Promise<void> {
    console.group(`🖼️ 测试单个图片代理: ${url}`);

    try {
      const startTime = Date.now();
      const proxiedUrl = await proxyImage(url);
      const endTime = Date.now();

      console.log(`原始URL: ${url}`);
      console.log(
        `代理后URL: ${proxiedUrl.substring(0, 100)}${
          proxiedUrl.length > 100 ? "..." : ""
        }`
      );
      console.log(`处理时间: ${endTime - startTime}ms`);
      console.log(`是否使用了代理: ${proxiedUrl !== url ? "✅ 是" : "❌ 否"}`);

      // 如果是 data URL，显示一些统计信息
      if (proxiedUrl.startsWith("data:")) {
        console.log(`Data URL 大小: ${Math.round(proxiedUrl.length / 1024)}KB`);
        console.log(`图片格式: ${proxiedUrl.split(";")[0].split(":")[1]}`);
      }
    } catch (error) {
      console.error("❌ 代理失败:", error);
    }

    console.groupEnd();
  }

  /**
   * 测试批量图片代理功能
   */
  static async testBatchImageProxy(): Promise<void> {
    console.group("📦 测试批量图片代理功能");

    const startTime = Date.now();
    const results = await Promise.allSettled(
      TEST_IMAGES.map((url) => proxyImage(url))
    );
    const endTime = Date.now();

    console.log(`总处理时间: ${endTime - startTime}ms`);
    console.log(
      `成功数量: ${results.filter((r) => r.status === "fulfilled").length}/${
        results.length
      }`
    );

    results.forEach((result, index) => {
      const url = TEST_IMAGES[index];
      if (result.status === "fulfilled") {
        const proxiedUrl = result.value;
        console.log(`✅ ${index + 1}. 成功 - ${url}`);
        console.log(`   代理: ${proxiedUrl !== url ? "是" : "否"}`);
      } else {
        console.log(`❌ ${index + 1}. 失败 - ${url}`);
        console.log(`   错误: ${result.reason}`);
      }
    });

    console.groupEnd();
  }

  /**
   * 测试文章处理器功能
   */
  static async testArticleProcessor(): Promise<void> {
    console.group("📄 测试文章处理器功能");

    try {
      const startTime = Date.now();
      const processedContent = await ArticleProcessor.processContent(
        TEST_HTML_CONTENT,
        "https://example.com/article/123",
        {
          enableImageProxy: true,
          enableImageOptimization: true,
          timeout: 10000,
        }
      );
      const endTime = Date.now();

      console.log(`处理时间: ${endTime - startTime}ms`);
      console.log("原始内容:");
      console.log(TEST_HTML_CONTENT);
      console.log("");
      console.log("处理后内容:");
      console.log(processedContent);

      // 检查是否正确处理了脚本标签
      const hasScript = processedContent.includes("<script");
      console.log(`脚本标签已移除: ${!hasScript ? "✅ 是" : "❌ 否"}`);

      // 检查是否正确处理了图片
      const imgCount = (processedContent.match(/<img/g) || []).length;
      console.log(`图片数量: ${imgCount}`);
    } catch (error) {
      console.error("❌ 文章处理失败:", error);
    }

    console.groupEnd();
  }

  /**
   * 测试缩略图提取功能
   */
  static async testThumbnailExtraction(): Promise<void> {
    console.group("🖼️ 测试缩略图提取功能");

    try {
      const startTime = Date.now();
      const thumbnail = await ArticleProcessor.extractThumbnail(
        TEST_HTML_CONTENT,
        "https://example.com/article/123"
      );
      const endTime = Date.now();

      console.log(`处理时间: ${endTime - startTime}ms`);
      console.log(
        `提取的缩略图: ${
          thumbnail ? thumbnail.substring(0, 100) + "..." : "无"
        }`
      );

      if (thumbnail) {
        console.log(
          `缩略图类型: ${
            thumbnail.startsWith("data:") ? "Data URL" : "HTTP URL"
          }`
        );
      }
    } catch (error) {
      console.error("❌ 缩略图提取失败:", error);
    }

    console.groupEnd();
  }

  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<void> {
    console.clear();
    console.log("🚀 开始运行图片代理功能测试");
    console.log("=====================================");

    // 检查环境
    console.group("🔧 环境检查");
    console.log(
      `uTools 环境: ${
        typeof (window as any).utoolsAPI !== "undefined" ? "✅ 是" : "❌ 否"
      }`
    );
    console.log(
      `图片代理 API: ${
        typeof (window as any).utoolsAPI?.imageProxy !== "undefined"
          ? "✅ 可用"
          : "❌ 不可用"
      }`
    );
    console.log(
      `ubrowser API: ${
        typeof (window as any).utoolsAPI?.ubrowser !== "undefined"
          ? "✅ 可用"
          : "❌ 不可用"
      }`
    );
    console.groupEnd();

    // 运行各项测试
    this.testImageProxyDetection();
    await this.testSingleImageProxy(TEST_IMAGES[0]);
    await this.testBatchImageProxy();
    await this.testArticleProcessor();
    await this.testThumbnailExtraction();

    console.log("=====================================");
    console.log("✅ 所有测试完成");
  }

  /**
   * 创建测试图片元素
   */
  static createTestImageElement(url: string): HTMLImageElement {
    const img = document.createElement("img");
    img.src = url;
    img.style.maxWidth = "200px";
    img.style.margin = "10px";
    img.style.border = "1px solid #ccc";
    img.alt = "测试图片";

    img.onload = () => {
      console.log(`✅ 图片加载成功: ${url}`);
    };

    img.onerror = () => {
      console.log(`❌ 图片加载失败: ${url}`);
    };

    return img;
  }

  /**
   * 在页面中显示测试图片
   */
  static displayTestImages(): void {
    const container = document.createElement("div");
    container.style.position = "fixed";
    container.style.top = "10px";
    container.style.right = "10px";
    container.style.width = "300px";
    container.style.maxHeight = "80vh";
    container.style.overflow = "auto";
    container.style.backgroundColor = "white";
    container.style.border = "2px solid #333";
    container.style.padding = "10px";
    container.style.zIndex = "9999";

    const title = document.createElement("h3");
    title.textContent = "图片代理测试";
    title.style.margin = "0 0 10px 0";
    container.appendChild(title);

    const closeBtn = document.createElement("button");
    closeBtn.textContent = "关闭";
    closeBtn.style.float = "right";
    closeBtn.onclick = () => container.remove();
    title.appendChild(closeBtn);

    TEST_IMAGES.forEach((url, index) => {
      const label = document.createElement("p");
      label.textContent = `测试图片 ${index + 1}:`;
      label.style.margin = "10px 0 5px 0";
      label.style.fontSize = "12px";
      container.appendChild(label);

      const img = this.createTestImageElement(url);
      container.appendChild(img);
    });

    document.body.appendChild(container);
  }
}

// 将测试工具挂载到全局对象，方便在控制台中使用
(window as any).ImageProxyTester = ImageProxyTester;

// 提供快捷方法
(window as any).testImageProxy = () => ImageProxyTester.runAllTests();
(window as any).showTestImages = () => ImageProxyTester.displayTestImages();

console.log("🔧 图片代理测试工具已加载");
console.log("使用方法:");
console.log("  testImageProxy() - 运行所有测试");
console.log("  showTestImages() - 显示测试图片");
console.log("  ImageProxyTester.runAllTests() - 完整测试");
